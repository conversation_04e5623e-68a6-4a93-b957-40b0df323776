# File: agents/mcq_extractor.py
import logging
import os
import json
import asyncio
import gc
import psutil
from typing import Dict, List, Any, Optional
import traceback
from sqlalchemy import text
import requests

import config
from db_config.db import get_session, CONTENT_SCHEMA
from agents.utils.pdf_helpers import PDFImageConverter
from agents.core.extractor import ExtractorAgent
from utils.timing_decorators import time_workflow
from .utils.image_extractor import extract_mcq_images
from agents.schemas.agent_prompts import question_extractor_prompt
from utils.s3_utils import get_s3_path, read_file_from_s3, upload_file_to_s3
import re
from agents.utils.helpers import smart_format_json
# Get logger instance
logger = logging.getLogger(__name__)


def get_memory_usage():
    """Get current memory usage in MB"""
    try:
        process = psutil.Process()
        memory_info = process.memory_info()
        return memory_info.rss / 1024 / 1024  # Convert to MB
    except Exception as e:
        logger.warning(f"Could not get memory usage: {e}")
        return 0


def get_system_memory_info():
    """Get system memory information"""
    try:
        memory = psutil.virtual_memory()
        return {
            'total': memory.total / 1024 / 1024,  # MB
            'available': memory.available / 1024 / 1024,  # MB
            'percent': memory.percent,
            'used': memory.used / 1024 / 1024  # MB
        }
    except Exception as e:
        logger.warning(f"Could not get system memory info: {e}")
        return {'total': 0, 'available': 0, 'percent': 0, 'used': 0}


def force_garbage_collection(aggressive: bool = False):
    """Force garbage collection to free up memory"""
    try:
        memory_before = get_memory_usage()

        # Force garbage collection multiple times for better cleanup
        collected_total = 0
        cycles = 5 if aggressive else 3

        for i in range(cycles):
            collected = gc.collect()
            collected_total += collected
            if collected == 0 and i > 0:  # Allow at least one cycle
                break

        memory_after = get_memory_usage()
        memory_freed = memory_before - memory_after

        logger.info(f"Garbage collection (aggressive={aggressive}) freed {collected_total} objects across {i+1} cycles, "
                   f"memory: {memory_before:.2f}MB → {memory_after:.2f}MB (freed {memory_freed:.2f}MB)")
        return collected_total
    except Exception as e:
        logger.warning(f"Error during garbage collection: {e}")
        return 0


def complete_memory_cleanup():
    """Perform optimized memory cleanup and return memory statistics"""
    try:
        memory_before = get_memory_usage()
        system_before = get_system_memory_info()

        # Optimized: Reduced logging for performance
        if getattr(config, 'SHOW_LOGS', True):
            logger.info(f"Starting memory cleanup - Process: {memory_before:.2f}MB, System: {system_before['percent']:.1f}%")

        # Optimized: Reduced garbage collection cycles from 5 to 3
        total_collected = 0
        for cycle in range(3):  # Reduced from 5 to 3 cycles
            collected = force_garbage_collection(aggressive=(cycle == 0))  # Only first cycle is aggressive
            total_collected += collected
            if collected == 0 and cycle > 0:  # Allow at least 1 cycle
                break

        # Optimized: Simplified cleanup - removed expensive operations
        import sys

        # Clear type cache only if available
        if hasattr(sys, '_clear_type_cache'):
            sys._clear_type_cache()

        # Optimized: Removed expensive module cleanup and malloc_trim

        memory_after = get_memory_usage()
        system_after = get_system_memory_info()

        memory_freed = memory_before - memory_after
        system_freed = system_before['percent'] - system_after['percent']

        cleanup_stats = {
            'memory_before_mb': memory_before,
            'memory_after_mb': memory_after,
            'memory_freed_mb': memory_freed,
            'system_before_percent': system_before['percent'],
            'system_after_percent': system_after['percent'],
            'system_freed_percent': system_freed,
            'objects_collected': total_collected,
            'cleanup_effective': memory_freed > 0 or total_collected > 0,
            'cleanup_cycles': cycle + 1
        }

        # Optimized: Reduced logging for performance
        if getattr(config, 'SHOW_LOGS', True):
            logger.info(f"Memory cleanup finished - Process: {memory_before:.2f}MB → {memory_after:.2f}MB "
                       f"(freed {memory_freed:.2f}MB), Objects: {total_collected}")

        return cleanup_stats
    except Exception as e:
        logger.error(f"Error during memory cleanup: {e}")
        return {
            'memory_before_mb': 0,
            'memory_after_mb': 0,
            'memory_freed_mb': 0,
            'system_before_percent': 0,
            'system_after_percent': 0,
            'system_freed_percent': 0,
            'objects_collected': 0,
            'cleanup_effective': False,
            'error': str(e)
        }


def check_memory_limit(limit_mb: int = None, critical_check: bool = False):
    """Enhanced memory checking with multiple thresholds"""
    try:
        if limit_mb is None:
            limit_mb = getattr(config, 'MAX_MEMORY_MB', 1024)

        current_memory = get_memory_usage()
        system_memory = get_system_memory_info()

        # Get thresholds from config
        critical_mb = getattr(config, 'CRITICAL_MEMORY_MB', 1280)
        safe_mb = getattr(config, 'SAFE_MEMORY_MB', 768)
        force_gc_threshold = getattr(config, 'FORCE_GC_THRESHOLD', 0.8)

        logger.info(f"Memory usage: {current_memory:.2f} MB | System: {system_memory['percent']:.1f}% used | Available: {system_memory['available']:.2f} MB")

        # Critical memory check - immediate stop
        if current_memory > critical_mb or system_memory['percent'] > 90:
            logger.error(f"CRITICAL MEMORY: Process={current_memory:.2f}MB (limit={critical_mb}MB), System={system_memory['percent']:.1f}%")
            raise MemoryError(f"Critical memory usage: Process={current_memory:.2f}MB, System={system_memory['percent']:.1f}%")

        # Force garbage collection if above threshold
        if current_memory > (limit_mb * force_gc_threshold):
            logger.warning(f"Memory usage ({current_memory:.2f} MB) above GC threshold ({limit_mb * force_gc_threshold:.2f} MB), forcing cleanup")
            force_garbage_collection()

            # Check again after cleanup
            new_memory = get_memory_usage()
            logger.info(f"Memory usage after cleanup: {new_memory:.2f} MB")

            # If still too high after cleanup, raise error
            if new_memory > limit_mb:
                if critical_check:
                    raise MemoryError(f"Memory usage ({new_memory:.2f} MB) exceeds limit ({limit_mb} MB) even after cleanup")
                else:
                    logger.warning(f"Memory usage ({new_memory:.2f} MB) still above limit ({limit_mb} MB) after cleanup")

            return new_memory

        return current_memory
    except MemoryError:
        raise
    except Exception as e:
        logger.error(f"Error checking memory limit: {e}")
        raise


def safe_read_json(file_path: str) -> Any:
    """
    Safely read a JSON file and ensure it's closed immediately.

    Args:
        file_path: Path to the JSON file

    Returns:
        The parsed JSON content or None if there was an error
    """
    f = None
    try:
        # Open the file and read its content
        f = open(file_path, "r", encoding="utf-8")
        data = json.load(f)
        return data
    except Exception as e:
        logger.error(f"Error reading JSON file {file_path}: {e}")
        logger.error(traceback.format_exc())
        return None
    finally:
        # Ensure the file is closed even if an exception occurs
        logger.info("closing file")
        if f is not None:
            f.close()


def safe_write_json(file_path: str, data: Any, indent: int = 2, ensure_ascii: bool = False) -> bool:
    """
    Safely write data to a JSON file and ensure it's closed immediately.

    Args:
        file_path: Path to the JSON file
        data: Data to write to the file
        indent: Indentation level for the JSON file
        ensure_ascii: Whether to ensure ASCII encoding

    Returns:
        True if the file was written successfully, False otherwise
    """
    f = None
    try:
        # Open the file and write the data
        f = open(file_path, "w", encoding="utf-8")
        json.dump(data, f, indent=indent, ensure_ascii=ensure_ascii)
        return True
    except Exception as e:
        logger.error(f"Error writing JSON file {file_path}: {e}")
        logger.error(traceback.format_exc())
        return False
    finally:
        logger.info("closing file")
        # Ensure the file is closed even if an exception occurs
        if f is not None:
            f.close()

class MCQExtractor:
    """
    Manages the extraction of MCQs from PDF resources with enhanced memory management.
    """

    def __init__(self, max_concurrent_extractions: int = None):
        """
        Initialize the MCQExtractor.

        Args:
            max_concurrent_extractions: Maximum number of concurrent extractions to run in parallel (uses config value if None)
        """
        self.max_concurrent_extractions = max_concurrent_extractions or getattr(config, 'MAX_CONCURRENT_EXTRACTIONS', 2)
        self.memory_check_interval = getattr(config, 'MEMORY_CHECK_INTERVAL', 3)
        self.pdf_zoom_factor = getattr(config, 'PDF_ZOOM_FACTOR', 1.5)

        # Log initialization with memory-safe settings
        logger.info(f"MCQExtractor initialized with max_concurrent_extractions={self.max_concurrent_extractions}, "
                   f"memory_check_interval={self.memory_check_interval}, pdf_zoom_factor={self.pdf_zoom_factor}")

        # Log current memory status
        initial_memory = get_memory_usage()
        system_memory = get_system_memory_info()
        logger.info(f"Initial memory status - Process: {initial_memory:.2f} MB, System: {system_memory['percent']:.1f}% used")

    def monitor_memory_during_processing(self, context: str = "", force_cleanup_threshold: float = 0.9):
        """Optimized memory monitoring with reduced overhead"""
        try:
            current_memory = get_memory_usage()

            # Get thresholds from config
            max_memory = getattr(config, 'MAX_MEMORY_MB', 1024)
            critical_memory = getattr(config, 'CRITICAL_MEMORY_MB', 1280)

            # Calculate memory usage percentages
            memory_usage_percent = current_memory / max_memory

            # Optimized: Reduced logging frequency for performance
            if getattr(config, 'SHOW_LOGS', True) and context:
                logger.info(f"Memory monitor {context} - Process: {current_memory:.2f}MB ({memory_usage_percent:.1%})")

            # Check if we need to perform cleanup (increased threshold from 0.85 to 0.9)
            if memory_usage_percent >= force_cleanup_threshold:
                if getattr(config, 'SHOW_LOGS', True):
                    logger.warning(f"Memory usage ({current_memory:.2f}MB) above cleanup threshold ({force_cleanup_threshold:.1%})")

                # Perform optimized garbage collection (less aggressive)
                collected = force_garbage_collection(aggressive=False)

                # Check memory after cleanup
                new_memory = get_memory_usage()
                memory_freed = current_memory - new_memory

                if getattr(config, 'SHOW_LOGS', True):
                    logger.info(f"Cleanup complete - Memory: {current_memory:.2f}MB → {new_memory:.2f}MB (freed {memory_freed:.2f}MB)")

                return {
                    'cleanup_performed': True,
                    'memory_before': current_memory,
                    'memory_after': new_memory,
                    'memory_freed': memory_freed,
                    'objects_collected': collected
                }

            # Check for critical memory levels
            if current_memory >= critical_memory:
                logger.error(f"CRITICAL MEMORY LEVEL: {current_memory:.2f}MB >= {critical_memory}MB")
                raise MemoryError(f"Critical memory usage: {current_memory:.2f}MB")

            return {
                'cleanup_performed': False,
                'current_memory': current_memory,
                'memory_usage_percent': memory_usage_percent
            }

        except MemoryError:
            raise
        except Exception as e:
            logger.error(f"Error during memory monitoring: {e}")
            return {'error': str(e)}

    def log_memory_status(self, context: str = ""):
        """Log current memory status with context"""
        try:
            process_memory = get_memory_usage()
            system_memory = get_system_memory_info()
            logger.info(f"Memory status {context} - Process: {process_memory:.2f} MB, "
                       f"System: {system_memory['percent']:.1f}% used, Available: {system_memory['available']:.2f} MB")
        except Exception as e:
            logger.warning(f"Could not log memory status: {e}")

    def cleanup_extraction_variables(self, local_vars: dict = None):
        """Optimized cleanup of extraction-related variables"""
        try:
            if getattr(config, 'SHOW_LOGS', True):
                logger.info("Starting cleanup of extraction variables...")

            # Optimized: Reduced list of critical variables to clean up
            cleanup_vars = [
                # Most critical memory consumers
                'pdf_converter', 'conversion_result', 'col_img_urls', 'results',
                'futures', 'task_args', 'json_content', 'questions', 'explanations',
                'mcq_extractor', 'extractor', 'data', 'response', 'content'
            ]

            # Clean up local variables if provided
            cleaned_count = 0
            if local_vars:
                for var_name in cleanup_vars:
                    if var_name in local_vars:
                        try:
                            # Optimized: Removed expensive size calculation
                            del local_vars[var_name]
                            cleaned_count += 1
                        except Exception as e:
                            if getattr(config, 'SHOW_LOGS', True):
                                logger.warning(f"Could not delete variable {var_name}: {e}")

                # Optimized: Simplified pattern cleanup
                pattern_vars = [var for var in local_vars.keys() if any(pattern in var.lower()
                               for pattern in ['temp', 'cache', 'buffer'])]

                for var_name in pattern_vars:
                    if var_name not in cleanup_vars:  # Don't double-delete
                        try:
                            del local_vars[var_name]
                            cleaned_count += 1
                        except:
                            pass

                if getattr(config, 'SHOW_LOGS', True):
                    logger.info(f"Cleaned up {cleaned_count} local variables")

            # Clear any instance variables that might be holding references
            instance_vars = ['pdf_converter', 'extractor', 'validator']
            for var_name in instance_vars:
                if hasattr(self, var_name):
                    try:
                        delattr(self, var_name)
                        if getattr(config, 'SHOW_LOGS', True):
                            logger.info(f"Cleared instance variable {var_name}")
                    except:
                        pass

            # Optimized garbage collection
            cleanup_stats = complete_memory_cleanup()

            return cleanup_stats

        except Exception as e:
            logger.error(f"Error during variable cleanup: {e}")
            return None

    def get_resume_info(self, resource_id: str, book_id: str, chapter_id: str, col_img_urls: List[str]) -> Dict:
        """
        Check existing JSON files to determine which columns still need processing.

        Args:
            resource_id: Resource ID
            book_id: Book ID
            chapter_id: Chapter ID
            col_img_urls: List of all column image URLs

        Returns:
            Dict containing resume information
        """
        try:
            output_dir = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id), str(resource_id), "mcq_extraction")

            # Check if output directory exists
            if not os.path.exists(output_dir):
                return {
                    "can_resume": False,
                    "processed_count": 0,
                    "remaining_urls": col_img_urls,
                    "existing_files": [],
                    "message": "No previous extraction found"
                }

            # Get existing JSON files
            existing_files = [f for f in os.listdir(output_dir) if f.endswith(".json") and f.startswith(str(resource_id))]

            if not existing_files:
                return {
                    "can_resume": False,
                    "processed_count": 0,
                    "remaining_urls": col_img_urls,
                    "existing_files": [],
                    "message": "No existing JSON files found"
                }

            # Extract page and column info from existing files
            processed_columns = set()
            valid_existing_files = []

            import re
            for file_name in existing_files:
                file_path = os.path.join(output_dir, file_name)

                # Extract page and column from filename
                page_match = re.search(r'page_([0-9]+)', file_name)
                col_match = re.search(r'col_([0-9]+)', file_name)

                if page_match and col_match:
                    page_num = page_match.group(1)
                    col_num = col_match.group(1)

                    # Check if the file has valid content
                    try:
                        data = safe_read_json(file_path)
                        if data and "content" in data and data["content"]:
                            processed_columns.add((page_num, col_num))
                            valid_existing_files.append(file_name)
                        else:
                            # Invalid file, will be reprocessed
                            logger.warning(f"Found invalid JSON file: {file_name}")
                    except Exception as e:
                        logger.warning(f"Error reading JSON file {file_name}: {e}")

            # Determine which column images still need processing
            remaining_urls = []
            for url in col_img_urls:
                # Extract page and column from URL
                page_match = re.search(r'page_([0-9]+)', os.path.basename(url))
                col_match = re.search(r'col_([0-9]+)', os.path.basename(url))

                if page_match and col_match:
                    page_num = page_match.group(1)
                    col_num = col_match.group(1)

                    # If this column hasn't been processed, add to remaining
                    if (page_num, col_num) not in processed_columns:
                        remaining_urls.append(url)
                else:
                    # If we can't extract page/col info, include it for processing
                    remaining_urls.append(url)

            processed_count = len(col_img_urls) - len(remaining_urls)
            can_resume = processed_count > 0

            return {
                "can_resume": can_resume,
                "processed_count": processed_count,
                "total_count": len(col_img_urls),
                "remaining_count": len(remaining_urls),
                "remaining_urls": remaining_urls,
                "existing_files": valid_existing_files,
                "processed_columns": list(processed_columns),
                "message": f"Found {processed_count} already processed columns, {len(remaining_urls)} remaining"
            }

        except Exception as e:
            logger.error(f"Error checking resume info: {e}")
            return {
                "can_resume": False,
                "processed_count": 0,
                "remaining_urls": col_img_urls,
                "existing_files": [],
                "message": f"Error checking resume info: {str(e)}"
            }

    async def check_resource_processed(self, res_id: str) -> Dict:
        """
        Check if a resource has already been processed by looking for output files.

        Args:
            res_id: Resource ID

        Returns:
            Dict: Status of the resource processing
        """
        try:
            # Get resource details
            resource_details = await self.get_resource_details(res_id)

            if resource_details["status"] != "success":
                return resource_details

            resource_id = resource_details["resource_id"]
            chapter_id = resource_details["chapter_id"]
            book_id = resource_details["book_id"]

            # Check if output directory exists
            output_dir = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id), str(resource_id), "mcq_extraction")

            if not os.path.exists(output_dir):
                return {
                    "status": "not_processed",
                    "message": f"Resource {res_id} has not been processed yet",
                    "output_dir": output_dir
                }

            # Count JSON files in the output directory
            json_files = [f for f in os.listdir(output_dir) if f.endswith(".json")]

            return {
                "status": "processed",
                "message": f"Resource {res_id} has been processed with {len(json_files)} output files",
                "output_dir": output_dir,
                "file_count": len(json_files),
                "files": json_files
            }

        except Exception as e:
            logger.error(f"Error checking if resource {res_id} has been processed: {e}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    async def get_resource_details(self, res_id: str) -> Dict:
        """
        Get resource details from the database.

        Args:
            res_id: Resource ID

        Returns:
            Dict: Resource details including id, chapter_id, res_link, resource_name, and book_id
        """
        try:
            # Get a database session for the wscontent schema
            db_session = next(get_session(CONTENT_SCHEMA))
            try:
                # Query the resource_dtl table to get resource details
                resource_query = text("""
                    SELECT id, chapter_id, res_link, resource_name
                    FROM wscontent.resource_dtl
                    WHERE id = :res_id
                    LIMIT 1
                """)

                resource_result = db_session.execute(resource_query, {"res_id": res_id})
                resource_row = resource_result.fetchone()

                if not resource_row:
                    logger.warning(f"No resource found with ID: {res_id}")
                    return {"status": "error", "message": f"Resource with ID {res_id} not found"}

                # Extract resource details
                resource_id = resource_row[0]
                chapter_id = resource_row[1]
                file_path = resource_row[2]
                resource_name = resource_row[3]

                # Query the chapters_mst table to get the book_id
                chapter_query = text("""
                    SELECT book_id
                    FROM wscontent.chapters_mst
                    WHERE id = :chapter_id
                    LIMIT 1
                """)

                chapter_result = db_session.execute(chapter_query, {"chapter_id": chapter_id})
                chapter_row = chapter_result.fetchone()

                if not chapter_row:
                    logger.warning(f"No chapter found with ID: {chapter_id}")
                    return {"status": "error", "message": f"Chapter with ID {chapter_id} not found"}

                # Extract book_id
                book_id = chapter_row[0]

                return {
                    "status": "success",
                    "resource_id": resource_id,
                    "chapter_id": chapter_id,
                    "file_path": file_path,
                    "resource_name": resource_name,
                    "book_id": book_id
                }
            finally:
                # Close the database session
                db_session.close()
                logger.debug("Database session closed after resource and chapter queries")
        except Exception as e:
            logger.error(f"Error getting resource details: {e}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    @time_workflow("MCQ Extraction Workflow")
    async def extract_mcqs_from_resource(self, res_id: str, workflow_id: str = None, username: str = None) -> Dict:
        """
        Extract MCQs from a resource.

        Args:
            res_id: Resource ID
            workflow_id: Optional workflow ID for timing (added by the time_workflow decorator)
            username: Username of the user performing the extraction

        Returns:
            Dict: Result of the extraction process
        """
        # Store initial memory state for comparison at the end
        initial_memory_state = {
            'process_memory': get_memory_usage(),
            'system_memory': get_system_memory_info()
        }

        try:
            # Enhanced initial memory check
            memory_limit = getattr(config, 'MAX_MEMORY_MB', 1024)
            initial_memory = check_memory_limit(memory_limit, critical_check=True)
            logger.info(f"Starting MCQ extraction - Initial memory: Process={initial_memory:.2f}MB, "
                       f"System={initial_memory_state['system_memory']['percent']:.1f}% (limit: {memory_limit}MB)")

            # Step 1: Get resource details
            resource_details = await self.get_resource_details(res_id)

            if resource_details["status"] != "success":
                logger.error(f"Error getting resource details: {resource_details['message']}")
                return resource_details

            resource_id = resource_details["resource_id"]
            chapter_id = resource_details["chapter_id"]
            file_path = resource_details["file_path"]
            resource_name = resource_details["resource_name"]
            book_id = resource_details["book_id"]

            # Step 2: Check if the JSON file already exists in S3
            s3_json_path = f"supload/pdfextracts/{book_id}/{chapter_id}/{resource_id}/{resource_id}.json"
            full_s3_path = get_s3_path(s3_json_path)

            # Check if the file exists in S3
            content = read_file_from_s3(full_s3_path)
            if content is not None:
                logger.info(f"JSON file already exists in S3, skipping extraction")
                return {
                    "status": "success",
                    "message": f"JSON file already exists in S3",
                    "result_path": s3_json_path,
                    "from_cache": True
                }

            # Step 3: Convert PDF to column images with memory-safe settings
            logger.info("Starting PDF to image conversion with enhanced memory management")
            check_memory_limit(memory_limit, critical_check=True)  # Critical check before PDF conversion

            pdf_converter = PDFImageConverter()
            conversion_result = pdf_converter.convert_and_upload(
                pdf_path=file_path,
                book_id=book_id,
                chapter_id=chapter_id,
                res_id=resource_id,
                zoom=self.pdf_zoom_factor  # Use memory-safe zoom factor
            )

            if conversion_result["status"] != "success":
                return {"status": "error", "message": conversion_result["message"]}

            # Get the column image URLs
            col_img_urls = conversion_result.get("cropped_image_urls", [])
            if not col_img_urls:
                return {"status": "error", "message": "No column images found"}

            # Force cleanup after PDF conversion
            force_garbage_collection()
            self.log_memory_status("after PDF conversion")
            logger.info(f"PDF conversion completed, generated {len(col_img_urls)} column images")

            # Sort the column image URLs by page and column number
            def get_page_col_numbers(url):
                # Extract page and column numbers for sorting
                import re
                page_match = re.search(r'page_([0-9]+)', os.path.basename(url))
                col_match = re.search(r'col_([0-9]+)', os.path.basename(url))

                page_num = int(page_match.group(1)) if page_match else 0
                col_num = int(col_match.group(1)) if col_match else 0

                return (page_num, col_num)

            # Sort by page number first, then by column number
            col_img_urls.sort(key=get_page_col_numbers)

            # Step 4: Check for resume capability
            logger.info("Checking for existing processed columns to enable resume functionality...")
            resume_info = self.get_resume_info(resource_id, book_id, chapter_id, col_img_urls)

            if resume_info["can_resume"]:
                logger.info(f"✓ RESUME ENABLED: {resume_info['message']}")
                logger.info(f"✓ RESUME: Processing {resume_info['remaining_count']} remaining columns out of {resume_info['total_count']} total")
                logger.info(f"✓ RESUME: Found {len(resume_info['existing_files'])} existing JSON files")

                # Use only the remaining URLs for processing
                col_img_urls = resume_info["remaining_urls"]

                # Log which columns are being skipped
                if resume_info["processed_columns"]:
                    skipped_columns = ", ".join([f"page_{p}_col_{c}" for p, c in resume_info["processed_columns"]])
                    logger.info(f"✓ RESUME: Skipping already processed columns: {skipped_columns}")
            else:
                logger.info(f"○ RESUME: {resume_info['message']} - Starting fresh extraction")

            total_images = len(col_img_urls)

            if total_images == 0:
                logger.info("All columns have been processed, proceeding to merge existing JSON files")
                # Skip to merging step since all columns are already processed
            else:
                logger.info(f"Processing {total_images} column images")

            # Step 5: Create output directory
            output_dir = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id), str(resource_id), "mcq_extraction")
            os.makedirs(output_dir, exist_ok=True)

            # Step 6: Process images in parallel using ThreadPoolExecutor (only if there are images to process)
            results = []
            failed_tasks = []
            skipped_count = 0
            skipped_locations = []
            success_count = 0

            if total_images > 0:
                # Define a function to process a single image (non-async for ThreadPoolExecutor)
                def process_single_image_sync(args):
                    i, image_path = args
                    page_number = None
                    col_number = None

                    try:
                        # Extract page number and column number from the image path
                        page_name = os.path.basename(image_path)

                        # Use regex to extract page and column numbers more reliably
                        import re

                        # Extract page number
                        page_match = re.search(r'page_([0-9]+)', page_name)
                        page_number = page_match.group(1) if page_match else None

                        # Extract column number
                        col_match = re.search(r'col_([0-9]+)', page_name)
                        col_number = col_match.group(1) if col_match else None

                        if not page_number or not col_number:
                            logger.warning(f"Could not extract page or column number from {page_name}")
                            page_number = str(i + 1)
                            col_number = "1"

                        print(f"\nProcessing image {i+1}/{total_images} for page {page_number}, column {col_number}")
                        # Create a new extractor instance for thread safety
                        extractor = ExtractorAgent()

                        # Create the output file path
                        output_file = os.path.join(output_dir, f"{resource_id}_page_{page_number}_col_{col_number}.json")

                        # Check if the output file already exists (from a previous run)
                        if os.path.exists(output_file):
                            existing_data = safe_read_json(output_file)
                            if existing_data and "content" in existing_data:
                                logger.info(f"Successfully loaded existing result from {output_file}")
                                return {
                                    "page_number": page_number,
                                    "col_number": col_number,
                                    "file_path": output_file,
                                    "content": existing_data["content"],
                                    "status": "success",
                                    "from_cache": True
                                }
                            else:
                                logger.warning(f"Error loading existing result or missing content, will reprocess")

                        # Use a more reliable approach for running async code in threads
                        import asyncio

                        # Create a new event loop for this thread
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        try:
                            # Set a timeout for the async operation
                            async def run_with_timeout():
                                # Run the extraction with a timeout
                                return await asyncio.wait_for(
                                    extractor._extract_with_prompt([image_path], question_extractor_prompt(), md_parse=False, md_result=False),
                                    timeout=getattr(config, 'EXTRACTION_TIMEOUT_SECONDS', 900)  # Use config timeout
                                )

                            # Run the extraction in the current thread (blocking operation)
                            data, _ = loop.run_until_complete(run_with_timeout())
                            result = smart_format_json(data)

                            # Check if the result is a special 504 error response
                            if isinstance(result, dict) and result.get("status") == "skipped_due_to_504":
                                logger.warning(f"Skipping extraction for {result.get('context', '')} due to unrecoverable 504 error")
                                # Return a special error result that indicates this extraction was skipped
                                # Include detailed information about which page/column was skipped
                                return {
                                    "page_number": page_number,
                                    "col_number": col_number,
                                    "file_path": None,
                                    "content": result.get("message", "Skipped due to 504 Gateway Timeout"),
                                    "error_details": result.get("error_details", "Unknown error"),
                                    "status": "skipped",
                                    "skipped_reason": "504_gateway_timeout"
                                }

                            # Initialize variables
                            mappings = []
                            explanation_mappings = []
                            questions = []
                            explanations = []
                            parsed_result = None

                            # Extract mappings and data from result
                            if isinstance(result, dict):
                                mappings = result.get("mappings", [])
                                explanation_mappings = result.get("explanationMappings", [])
                                questions = result.get("questions", [])
                                explanations = result.get("explanations", [])
                                parsed_result = result
                            elif isinstance(result, str):
                                try:
                                    parsed_result = json.loads(result)
                                    if isinstance(parsed_result, dict):
                                        mappings = parsed_result.get("mappings", [])
                                        explanation_mappings = parsed_result.get("explanationMappings", [])
                                        questions = parsed_result.get("questions", [])
                                        explanations = parsed_result.get("explanations", [])
                                except json.JSONDecodeError:
                                    logger.warning(f"Could not parse result as JSON")

                            # Prepare image paths
                            page_id = f"page_{page_number}_col_{col_number}"
                            mcq_full_img_storage_path = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id), str(resource_id))
                            mcq_local_image_paths = [
                                os.path.join(mcq_full_img_storage_path, img)
                                for img in os.listdir(mcq_full_img_storage_path)
                                if img.lower().endswith(f"{page_id}.png")
                            ]
                            mcq_local_image_paths.sort(key=lambda x: natural_sort_key(os.path.basename(x)))

                            # Extract and upload images using both mappings
                            extracted_img_urls = extractor.extract_quiz_images_new(
                                mcq_local_image_paths,
                                book_id,
                                chapter_id,
                                res_id,
                                mappings,
                                explanation_mappings
                            )

                            # Map images to MCQs if questions array exists and is not empty
                            if questions:
                                mapped_questions = extractor.map_images_to_mcqs(questions, extracted_img_urls, "mcq")
                                if parsed_result:
                                    parsed_result["questions"] = mapped_questions
                            else:
                                logger.info("No questions to map to images")

                            # Map images to explanations if explanations array exists and is not empty
                            if explanations:
                                mapped_explanations = extractor.map_images_to_mcqs(explanations, extracted_img_urls, "explanation")
                                if parsed_result:
                                    parsed_result["explanations"] = mapped_explanations
                            else:
                                logger.info("No explanations to map to images")

                            # Use the updated result with mapped images
                            if parsed_result:
                                result = parsed_result

                            # Save the result to a JSON file
                            safe_write_json(output_file, {"content": result})

                            # Enhanced cleanup after processing is complete
                            del extractor
                            del parsed_result, questions, explanations, mappings, explanation_mappings
                            if 'mcq_local_image_paths' in locals():
                                del mcq_local_image_paths
                            if 'extracted_img_urls' in locals():
                                del extracted_img_urls
                            force_garbage_collection()

                            return {
                                "page_number": page_number,
                                "col_number": col_number,
                                "file_path": output_file,
                                "content": result,
                                "status": "success"
                            }
                        except asyncio.TimeoutError:
                            logger.error(f"Timeout processing image {i+1}/{total_images}")
                            return {
                                "page_number": page_number,
                                "col_number": col_number,
                                "file_path": None,
                                "content": "Operation timed out after 10 minutes",
                                "status": "error"
                            }
                        except Exception as inner_e:
                            logger.error(f"Error in LLM processing for image {i+1}/{total_images}: {str(inner_e)}")
                            logger.error(traceback.format_exc())
                            return {
                                "page_number": page_number,
                                "col_number": col_number,
                                "file_path": None,
                                "content": str(inner_e),
                                "status": "error"
                            }
                        finally:
                            # Always close the loop
                            try:
                                loop.close()
                            except Exception as loop_error:
                                logger.warning(f"Error closing event loop: {loop_error}")

                    except Exception as e:
                        logger.error(f"Error processing image {i+1}/{total_images}: {os.path.basename(image_path)} - {e}")
                        logger.error(traceback.format_exc())

                        # Even if there's an error, try to return a structured response
                        return {
                            "page_number": page_number if page_number else str(i + 1),
                            "col_number": col_number if col_number else "1",
                            "file_path": None,
                            "content": str(e),
                            "status": "error"
                        }

                # Create a list of arguments for each task
                task_args = [(i, image_path) for i, image_path in enumerate(col_img_urls)]

                import time
                start_time = time.time()

                # Execute tasks in parallel using ThreadPoolExecutor with better error handling
                from concurrent.futures import ThreadPoolExecutor, as_completed

                with ThreadPoolExecutor(max_workers=self.max_concurrent_extractions) as executor:
                    # Submit all tasks and get future objects
                    futures = []
                    for arg in task_args:
                        future = executor.submit(process_single_image_sync, arg)
                        futures.append((future, arg))

                    # Process results as they complete with enhanced memory monitoring
                    completed_count = 0
                    for future, arg in futures:
                        task_idx = arg[0]
                        task_path = arg[1]

                        # Optimized memory monitoring at configurable intervals (now every 10 tasks instead of 3)
                        if completed_count % self.memory_check_interval == 0:
                            try:
                                # Use the optimized memory monitoring function
                                monitor_result = self.monitor_memory_during_processing(
                                    context=f"task {completed_count+1}/{total_images}",
                                    force_cleanup_threshold=0.9  # Optimized: Cleanup at 90% of limit (was 80%)
                                )

                                if monitor_result.get('cleanup_performed'):
                                    if getattr(config, 'SHOW_LOGS', True):
                                        logger.info(f"Preventive cleanup performed during processing")

                                # Optimized: Simplified critical level check
                                if monitor_result.get('current_memory', 0) > getattr(config, 'CRITICAL_MEMORY_MB', 1280):
                                    raise MemoryError(f"Critical memory usage: {monitor_result.get('current_memory', 0):.2f}MB")

                            except MemoryError as me:
                                logger.error(f"Memory limit exceeded during processing at task {completed_count+1}: {me}")
                                # Cancel remaining futures gracefully
                                cancelled_count = 0
                                for remaining_future, _ in futures[completed_count:]:
                                    if remaining_future.cancel():
                                        cancelled_count += 1
                                logger.warning(f"Cancelled {cancelled_count} remaining tasks due to memory constraints")
                                # Force cleanup before raising
                                force_garbage_collection(aggressive=True)
                                raise

                        try:
                            # Get the result from the future with a reasonable timeout
                            # This ensures we don't wait forever, but gives enough time for completion
                            timeout_seconds = getattr(config, 'FUTURE_TIMEOUT_SECONDS', 1200)
                            result = future.result(timeout=timeout_seconds)
                            completed_count += 1

                            if result is not None:
                                results.append(result)
                            else:
                                failed_tasks.append(arg)

                                # Add a placeholder for the failed task
                                results.append({
                                    "page_number": str(task_idx + 1),
                                    "col_number": "1",
                                    "file_path": None,
                                    "content": "Task returned None",
                                    "status": "error"
                                })
                        except Exception as exc:
                            completed_count += 1
                            logger.error(f"Task {task_idx+1}/{total_images} generated an exception: {exc}")
                            logger.error(traceback.format_exc())
                            failed_tasks.append(arg)

                            # Create a placeholder result for failed tasks
                            results.append({
                                "page_number": str(task_idx + 1),
                                "col_number": "1",
                                "file_path": None,
                                "content": str(exc),
                                "status": "error"
                            })

                end_time = time.time()
                processing_time = end_time - start_time

                if failed_tasks:
                    logger.warning(f"Failed to process {len(failed_tasks)} out of {total_images} images")

                # Filter out None results (failed extractions)
                valid_results = [result for result in results if result is not None]

                # Count different types of results
                success_count = len([r for r in valid_results if r.get("status") == "success"])
                skipped_count = len([r for r in valid_results if r.get("status") == "skipped"])
                skipped_due_to_504 = [r for r in valid_results if r.get("status") == "skipped" and r.get("skipped_reason") == "504_gateway_timeout"]

                # Create a list of skipped pages/columns for reporting
                skipped_locations = []
                for r in skipped_due_to_504:
                    if r.get("page_number") and r.get("col_number"):
                        skipped_locations.append(f"page {r.get('page_number')} column {r.get('col_number')}")
                    elif r.get("content") and "page" in r.get("content"):
                        skipped_locations.append(r.get("content"))
                    else:
                        # If we can't determine the exact location, use any available information
                        location_info = r.get("content", "Unknown location")
                        skipped_locations.append(location_info)

                if skipped_locations:
                    logger.warning(f"Skipped {len(skipped_locations)} extractions due to 504 Gateway Timeout: {', '.join(skipped_locations)}")

                # Enhanced cleanup after parallel processing
                logger.info("Performing final cleanup after parallel processing")
                force_garbage_collection()

                # Final memory check with normal limit
                final_memory = check_memory_limit(memory_limit)
                logger.info(f"Memory usage after parallel processing cleanup: {final_memory:.2f} MB")
            else:
                # No images to process, set default values for when all columns are already processed
                success_count = resume_info.get("processed_count", 0)
                logger.info(f"All {success_count} columns already processed, skipping extraction phase")

            # Step 7: Merge all JSON files into a single consolidated JSON file
            merge_result = merge_json_files(output_dir, str(resource_id))

            if merge_result["status"] != "success":
                logger.error(f"Error merging JSON files: {merge_result['message']}")
                # Continue with the extraction result even if merging failed
            else:
                # Force cleanup after JSON merging
                force_garbage_collection()

            # Step 7: Upload the final JSON file to S3
            result_path = ""
            if merge_result["status"] == "success" and "file_path" in merge_result:
                logger.info(f"Uploading final JSON file to S3: {merge_result['file_path']}")
                from utils.s3_utils import upload_file_to_s3

                # Upload the file to S3
                s3_path = upload_file_to_s3(
                    local_file_path=merge_result["file_path"],
                    book_id=book_id,
                    chapter_id=chapter_id,
                    res_id=resource_id,
                    file_name=f"{resource_id}.json",
                    is_quiz_image=False  # This will put it in the main resId folder
                )

                if s3_path:
                    logger.info(f"Successfully uploaded final JSON to S3: {s3_path}")
                    result_path = s3_path
                else:
                    logger.error("Failed to upload final JSON to S3")

            # Process the merged JSON file with the external API calls
            if merge_result["status"] == "success":
                process_result = await self.process_merged_json(merge_result["file_path"], book_id, chapter_id, resource_id, username)

                if process_result["status"] == "success":
                    # Upload the processed JSON file to S3
                    s3_upload_path = upload_file_to_s3(
                        process_result["file_path"],
                        book_id,
                        chapter_id,
                        resource_id,
                        file_name=f"{resource_id}.json",
                        is_quiz_image=False
                    )

                    # Create a detailed message about skipped extractions and resume info
                    skipped_message = ""
                    if skipped_count > 0:
                        skipped_message = f" (skipped {skipped_count} due to 504 errors: {', '.join(skipped_locations)})"

                    # Add resume information to the message
                    resume_message = ""
                    if resume_info.get("can_resume"):
                        total_processed = resume_info.get("processed_count", 0) + success_count
                        total_columns = resume_info.get("total_count", total_images)
                        resume_message = f" (resumed from {resume_info.get('processed_count', 0)} previously processed columns)"

                    return {
                        "status": "success",
                        "message": f"Extracted and processed MCQs from {total_processed if resume_info.get('can_resume') else success_count}/{resume_info.get('total_count', total_images)} images{resume_message}{skipped_message}",
                        "total_images": resume_info.get("total_count", total_images),
                        "success_count": total_processed if resume_info.get("can_resume") else success_count,
                        "failure_count": (resume_info.get("total_count", total_images)) - (total_processed if resume_info.get("can_resume") else success_count) - skipped_count,
                        "skipped_count": skipped_count,
                        "skipped_locations": skipped_locations if skipped_locations else [],
                        "result_path": s3_upload_path if s3_upload_path else result_path,
                        "merge_result": merge_result,
                        "process_result": process_result,
                        "chapter_id": chapter_id,
                        "resource_id": resource_id,
                        "book_id": book_id,
                        "resume_info": resume_info
                    }
                else:
                    logger.error(f"Failed to process merged JSON file: {process_result['message']}")

            # Create a detailed message about skipped extractions and resume info
            skipped_message = ""
            if skipped_count > 0:
                skipped_message = f" (skipped {skipped_count} due to 504 errors: {', '.join(skipped_locations)})"

            # Add resume information to the message
            resume_message = ""
            if resume_info.get("can_resume"):
                total_processed = resume_info.get("processed_count", 0) + success_count
                resume_message = f" (resumed from {resume_info.get('processed_count', 0)} previously processed columns)"

            return {
                "status": "success",
                "message": f"Extracted MCQs from {total_processed if resume_info.get('can_resume') else success_count}/{resume_info.get('total_count', total_images)} images{resume_message}{skipped_message}",
                "total_images": resume_info.get("total_count", total_images),
                "success_count": total_processed if resume_info.get("can_resume") else success_count,
                "failure_count": (resume_info.get("total_count", total_images)) - (total_processed if resume_info.get("can_resume") else success_count) - skipped_count,
                "skipped_count": skipped_count,
                "skipped_locations": skipped_locations if skipped_locations else [],
                "result_path": result_path,
                "merge_result": merge_result,
                "chapter_id": chapter_id,
                "resource_id": resource_id,
                "book_id": book_id,
                "resume_info": resume_info
            }

        except MemoryError as me:
            logger.error(f"Memory error during MCQ extraction: {me}")
            self.log_memory_status("during memory error")
            # Force final cleanup
            cleanup_stats = self.cleanup_extraction_variables(locals())
            return {
                "status": "error",
                "message": f"Memory limit exceeded: {str(me)}",
                "cleanup_stats": cleanup_stats
            }
        except Exception as e:
            logger.error(f"Error extracting MCQs: {e}")
            logger.error(traceback.format_exc())
            self.log_memory_status("during error")
            # Force cleanup on error
            cleanup_stats = self.cleanup_extraction_variables(locals())
            return {
                "status": "error",
                "message": str(e),
                "cleanup_stats": cleanup_stats
            }
        finally:
            # Comprehensive final cleanup regardless of success or failure
            try:
                logger.info("🧹 Starting final comprehensive cleanup...")

                # Clean up all local variables
                cleanup_stats = self.cleanup_extraction_variables(locals())

                # Get final memory state
                final_memory_state = {
                    'process_memory': get_memory_usage(),
                    'system_memory': get_system_memory_info()
                }

                # Calculate memory recovery
                process_memory_change = final_memory_state['process_memory'] - initial_memory_state['process_memory']
                system_memory_change = final_memory_state['system_memory']['percent'] - initial_memory_state['system_memory']['percent']

                # Log comprehensive memory comparison
                logger.info(f" MCQ Extraction Complete - Memory Recovery Report:")
                logger.info(f"   Process Memory: {initial_memory_state['process_memory']:.2f}MB → {final_memory_state['process_memory']:.2f}MB "
                           f"(change: {process_memory_change:+.2f}MB)")
                logger.info(f"     System Memory: {initial_memory_state['system_memory']['percent']:.1f}% → {final_memory_state['system_memory']['percent']:.1f}% "
                           f"(change: {system_memory_change:+.1f}%)")
                logger.info(f"Cleanup Stats: {cleanup_stats}")

                # Check if memory was properly recovered (stricter threshold)
                if abs(process_memory_change) < 20:  # Within 20MB is good recovery
                    logger.info(" Memory recovery excellent - process memory returned to near initial state")
                elif abs(process_memory_change) < 50:  # Within 50MB is acceptable
                    logger.info(" Memory recovery successful - process memory returned to acceptable range")
                else:
                    logger.warning(f" Memory recovery incomplete - process memory changed by {process_memory_change:.2f}MB")

                if abs(system_memory_change) < 2:  # Within 2% is acceptable
                    logger.info(" System memory recovery successful")
                else:
                    logger.warning(f"System memory recovery incomplete - changed by {system_memory_change:.1f}%")

            except Exception as cleanup_error:
                logger.error(f"Error during final cleanup: {cleanup_error}")
                logger.error(traceback.format_exc())


    async def process_merged_json(self, json_file_path: str, book_id: str, chapter_id: str, resource_id: str, username: str) -> Dict:
        """
        Process the merged JSON file with external API calls.
        This implements the functionality that was previously done in the frontend JavaScript.

        Args:
            json_file_path: Path to the merged JSON file
            book_id: Book ID
            chapter_id: Chapter ID
            resource_id: Resource ID
            username: Username of the user performing the extraction

        Returns:
            Dict: Result of the processing
        """
        try:
            # Read the JSON file
            json_content = safe_read_json(json_file_path)
            if not json_content:
                return {"status": "error", "message": f"Failed to read JSON file {json_file_path}"}

            # Extract the arrays
            directions = json_content.get("directions", [])
            questions = json_content.get("questions", [])
            explanations = json_content.get("explanations", [])
            answer_keys = json_content.get("answerKeys", [])

            processed_directions = await self.process_directions(directions)

            for i, direction in enumerate(processed_directions):
                logger.info(f"Processed direction {i+1}: {direction}")

            # Map directions to questions
            self.map_directions_to_questions(processed_directions, questions)

            direction_count = 0
            for i, question in enumerate(questions):
                if question.get("directionId") is not None:
                    direction_count += 1
                    logger.info(f"Question {i+1} (number: {question.get('question_number')}) has directionId: {question.get('directionId')}")

            # Transform options format
            self.transform_options_format(questions)
            logger.info(f"Transformed options format")

            # Map answer keys to questions
            self.map_answer_keys_to_questions(answer_keys, questions)

            # Process MCQs with external API
            processed_mcqs = await self.process_mcqs(questions, chapter_id, resource_id, username)

            # Update the JSON content
            json_content["questions"] = questions
            json_content["directions"] = processed_directions
            json_content["explanations"] = explanations
            json_content["answerKeys"] = answer_keys

            # Save the updated JSON file
            if not safe_write_json(json_file_path, json_content):
                return {"status": "error", "message": f"Failed to write updated JSON file {json_file_path}"}

            updated_quiz_id = processed_mcqs[0]["additionalInformation"]["resId"]

            return {
                "status": "success",
                "message": f"Successfully processed merged JSON file",
                "file_path": json_file_path,
                "questions_count": len(questions),
                "explanations_count": len(explanations),
                "directions_count": len(processed_directions),
                "answer_keys_count": len(answer_keys),
                "processed_mcqs_count": len(processed_mcqs),
                "quiz_id": updated_quiz_id,
                "chapter_id": chapter_id,
                "resource_id": resource_id,
                "book_id": book_id
            }

        except Exception as e:
            logger.error(f"Error processing merged JSON file: {e}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    async def process_directions(self, directions: List[Dict]) -> List[Dict]:
        """
        Process directions with external API.
        This implements the functionality of the addDirections function in the frontend.

        Args:
            directions: List of direction objects

        Returns:
            List[Dict]: List of processed direction objects
        """
        try:
            # First transform text to passage
            for direction in directions:
                if "text" in direction:
                    direction["passage"] = direction["text"]
                    del direction["text"]

            # Process each direction with API call
            updated_directions = []

            for direction in directions:
                try:
                    request_json = {
                        "directions": direction["passage"]
                    }

                    # Get the base URL from config or use a default
                    base_url = getattr(config, 'BASE_URL', 'http://localhost:8000')

                    # Call the API for this direction
                    response = requests.post(
                        f"{base_url}/excel/processDirections",
                        headers={"Content-Type": "application/json"},
                        json=request_json
                    )
                    if response.status_code == 200:
                        response_data = response.json()
                        # Add the directionId to the direction object
                        direction_id = response_data.get("directionId")

                        if direction_id is not None:
                            # Convert to string if it's a number
                            if isinstance(direction_id, (int, float)):
                                direction_id = str(direction_id)

                            direction["directionId"] = direction_id

                            # Make sure the direction has an appliesTo field
                            if not direction.get("appliesTo"):
                                # Default to applying to all questions
                                direction["appliesTo"] = "1-100"
                                logger.info(f"Added default appliesTo field '1-100' to direction with ID {direction_id}")
                        else:
                            logger.warning(f"No directionId found in API response: {response_data}")
                        updated_directions.append(direction)
                    else:
                        logger.error(f"Error calling qq API: {response.status_code}")
                        # Still include the direction even if API call failed
                        updated_directions.append(direction)
                except Exception as e:
                    logger.error(f"Exception calling processDirections API: {e}")
                    # Still include the direction even if API call failed
                    updated_directions.append(direction)
            return updated_directions

        except Exception as e:
            logger.error(f"Error processing directions: {e}")
            logger.error(traceback.format_exc())
            return directions

    def map_directions_to_questions(self, directions: List[Dict], questions: List[Dict]) -> None:
        """
        Map directions to questions.
        This implements the functionality of the mapDirectionsToQuestions function in the frontend.

        Args:
            directions: List of direction objects
            questions: List of question objects
        """
        try:
            # First initialize all questions with directionId = null
            for question in questions:
                question["directionId"] = None

            # Map directions to questions
            for direction in directions:
                direction_id = direction.get("directionId")
                applies_to = direction.get("appliesTo")

                if not direction_id:
                    logger.warning(f"Skipping direction with missing directionId: {direction}")
                    continue

                if not applies_to:
                    logger.warning(f"Skipping direction with missing appliesTo: {direction}")
                    continue

                # Ensure direction_id is a string
                if not isinstance(direction_id, str):
                    direction_id = str(direction_id)

                # Skip special cases
                if applies_to.lower() in ["next", "all", "none"]:
                    logger.info(f"Skipping direction with special appliesTo value '{applies_to}'")
                    continue

                # Handle different formats of appliesTo field
                if "-" in applies_to:  # Range format like "1-6"
                    try:
                        start, end = applies_to.split("-")
                        start_num = int(start.strip())
                        end_num = int(end.strip())

                        # Find questions in this range and add directionsId
                        for question in questions:
                            question_num = question.get("question_number")
                            if question_num:
                                try:
                                    q_num = int(question_num)
                                    if start_num <= q_num <= end_num:
                                        question["directionId"] = direction_id
                                        logger.info(f"Added directionId {direction_id} to question {question_num}")
                                except Exception as e:
                                    logger.warning(f"Could not convert question_number {question_num} to int: {e}")
                    except Exception as e:
                        logger.warning(f"Error processing appliesTo range {applies_to}: {e}")

                elif "," in applies_to:  # List format like "1,2,3"
                    try:
                        question_nums = [int(num.strip()) for num in applies_to.split(",")]

                        # Find questions in this list and add directionsId
                        for question in questions:
                            question_num = question.get("question_number")
                            if question_num:
                                try:
                                    q_num = int(question_num)
                                    if q_num in question_nums:
                                        question["directionId"] = direction_id
                                        logger.info(f"Added directionId {direction_id} to question {question_num}")
                                except Exception as e:
                                    logger.warning(f"Could not convert question_number {question_num} to int: {e}")
                    except Exception as e:
                        logger.warning(f"Error processing appliesTo list {applies_to}: {e}")

                else:  # Single number format like "1"
                    try:
                        applies_to_num = int(applies_to.strip())

                        # Find the question with this number and add directionsId
                        for question in questions:
                            question_num = question.get("question_number")
                            if question_num:
                                try:
                                    q_num = int(question_num)
                                    if q_num == applies_to_num:
                                        question["directionId"] = direction_id
                                        logger.info(f"Added directionId {direction_id} to question {question_num}")
                                except Exception as e:
                                    logger.warning(f"Could not convert question_number {question_num} to int: {e}")
                    except Exception as e:
                        logger.warning(f"Error processing appliesTo single number {applies_to}: {e}")

            logger.info(f"Completed mapping directions to questions")

        except Exception as e:
            logger.error(f"Error mapping directions to questions: {e}")
            logger.error(traceback.format_exc())

    def map_answer_keys_to_questions(self, answer_keys: List[Dict], questions: List[Dict]) -> None:
        """
        Map answer keys to questions by adding a correctAnswer property to each question.

        Args:
            answer_keys: List of answer key objects
            questions: List of question objects
        """
        try:
            # Create a dictionary for quick lookup of answer keys by question number
            answer_key_dict = {}
            for answer_key in answer_keys:
                question_number = answer_key.get("question_number")
                answer = answer_key.get("answer")

                if not question_number or not answer:
                    logger.warning(f"Skipping answer key with missing question_number or answer: {answer_key}")
                    continue

                # Convert question_number to int if it's a string
                if isinstance(question_number, str) and question_number.isdigit():
                    question_number = int(question_number)

                # Store the answer in the dictionary
                answer_key_dict[question_number] = answer

            # Map answer keys to questions
            mapped_count = 0
            for question in questions:
                question_number = question.get("question_number")

                # Skip special cases
                if question_number == "__prev__" or question_number == "__next__":
                    logger.info(f"Skipping question with special question_number value '{question_number}'")
                    continue

                # Convert question_number to int if it's a string
                if isinstance(question_number, str) and question_number.isdigit():
                    question_number = int(question_number)

                # Find the matching answer key and add the correctAnswer property
                if question_number in answer_key_dict:
                    # Extract the answer value, removing parentheses if present
                    answer = answer_key_dict[question_number]
                    if isinstance(answer, str):
                        answer = answer.replace("(", "").replace(")", "")

                    question["correctAnswer"] = answer
                    mapped_count += 1
                    logger.info(f"Added correctAnswer '{answer}' to question {question_number}")
        except Exception as e:
            logger.error(f"Error mapping answer keys to questions: {e}")
            logger.error(traceback.format_exc())

    def map_explanations_to_questions(self, explanations: List[Dict], questions: List[Dict]) -> None:
        """
        Map explanations to questions.
        This implements the functionality of the mapExplanationsToQuestions function in the frontend.

        Args:
            explanations: List of explanation objects
            questions: List of question objects
        """
        try:
            # First initialize all questions with explanation = null
            for question in questions:
                question["explanation"] = None

            # Map explanations to questions
            for explanation in explanations:
                explanation_number = explanation.get("explanation_number")

                if not explanation_number:
                    logger.info(f"Skipping explanation with missing explanation_number: {explanation}")
                    continue

                # Skip special cases
                if explanation_number == "__prev__" or explanation_number == "__next__":
                    logger.info(f"Skipping explanation with special explanation_number value '{explanation_number}'")
                    continue

                # Find the matching question and add the explanation
                matching_question = None
                for question in questions:
                    try:
                        if question.get("question_number") == explanation_number:
                            matching_question = question
                            break
                    except Exception as e:
                        logger.warning(f"Error comparing question_number {question.get('question_number')} with explanation_number {explanation_number}: {e}")

                if matching_question:
                    matching_question["explanation"] = explanation.get("explanation")
                    logger.info(f"Added explanation to question {explanation_number}")
                else:
                    logger.warning(f"No matching question found for explanation {explanation_number}")

            logger.info(f"Completed mapping explanations to questions")
        except Exception as e:
            logger.error(f"Error mapping explanations to questions: {e}")
            logger.error(traceback.format_exc())

    def transform_options_format(self, questions: List[Dict]) -> None:
        """
        Transform options array in questions to individual op1, op2, op3, op4, op5 properties.
        This implements the functionality of the transformOptionsFormat function in the frontend.

        Args:
            questions: List of question objects
        """
        try:
            for question in questions:
                if isinstance(question.get("options"), list):
                    # Extract option values from the array and clean option labels
                    for index, option in enumerate(question["options"]):
                        # Create op1, op2, op3, etc. properties with cleaned and LaTeX-processed option text
                        cleaned_option = remove_option_labels(option)
                        processed_option = wrap_latex_expressions(cleaned_option)
                        question[f"op{index + 1}"] = processed_option

                    # Remove the original options array
                    del question["options"]

                    logger.info(f"Transformed, cleaned, and processed LaTeX for options in question {question.get('question_number')}")
                else:
                    logger.info(f"Question {question.get('question_number')} doesn't have options array")

            logger.info(f"Completed transforming options format")

        except Exception as e:
            logger.error(f"Error transforming options format: {e}")
            logger.error(traceback.format_exc())

    async def process_mcqs(self, questions: List[Dict], chapter_id: str, resource_id: str, username: str) -> List[Dict]:
        """
        Process MCQs with external API.
        This implements the functionality of the processMCQsWithProgress and addMCQ functions in the frontend.

        Args:
            questions: List of question objects
            chapter_id: Chapter ID
            resource_id: Resource ID
            username: Username of the user performing the extraction

        Returns:
            List[Dict]: List of processed MCQ objects
        """
        try:
            processed_mcqs = []
            quiz_id = "-1"  # Initialize quiz_id with "-1" for the first request

            # Process questions sequentially to maintain order
            for i, question in enumerate(questions):
                try:
                    # Get the directionId from the question
                    direction_id = question.get("directionId")
                    # Prepare the request JSON
                    # Get the question text, defaulting to empty string
                    question_text = question.get("text", "")
                    # If question text is empty, set it to "."
                    if question_text == "":
                        question_text = "."
                        logger.info(f"Empty question text for question {question.get('question_number')}, setting to '.'")

                    request_json = {
                        "chapterId": chapter_id,
                        "resId": quiz_id,  # Use quiz_id which starts as "-1" and gets updated after first response
                        "Question": question_text,
                        "option1": question.get("op1", ""),
                        "option2": question.get("op2", ""),
                        "option3": question.get("op3", ""),
                        "option4": question.get("op4", ""),
                        "answerDescription": question.get("explanation", ""),
                        "correctAnswer": question.get("correctAnswer", ""),
                        "question_images": question.get("question_images", []),
                        "option_images": question.get("option_images", []),
                        "explanation_images": question.get("explanation_images", []),
                        "username": username
                    }
                    # Only add directionId if it's not None
                    if direction_id is not None:
                        request_json["directionId"] = direction_id
                    # Get the base URL from config or use a default
                    base_url = getattr(config, 'BASE_URL', 'http://localhost:8000')

                    print(request_json)
                    # Call the API for this MCQ
                    response = requests.post(
                        f"{base_url}/excel/processMCQ",
                        headers={"Content-Type": "application/json"},
                        json=request_json
                    )
                    if response.status_code == 200:
                        response_data = response.json()
                        processed_mcqs.append(response_data)

                        # Update quiz_id from the first response for subsequent requests
                        if i == 0 and response_data.get("status") == "OK" and response_data.get("additionalInformation") and response_data["additionalInformation"].get("resId"):
                            quiz_id = response_data["additionalInformation"]["resId"]
                    else:
                        logger.error(f"Error processing MCQ {response.status_code}")
                except Exception as e:
                    logger.error(f"Exception calling processMCQ API for MCQ {question.get('question_number')}: {e}")

            logger.info(f"Processed {len(processed_mcqs)}/{len(questions)} MCQs")
            return processed_mcqs

        except Exception as e:
            logger.error(f"Error processing MCQs: {e}")
            logger.error(traceback.format_exc())
            return []


def natural_sort_key(s):
    # Extract the number from the filename using regex
    numbers = re.findall(r'(\d+)', s)
    if numbers:
        return int(numbers[0])
    return s


def remove_duplicate_explanations(json_data):
    """
    Check for duplicate explanations in the explanations array, map the first occurrence to the
    corresponding question, and remove duplicates from the explanations array.

    Args:
        json_data: The processed JSON data containing questions, explanations, directions, and answerKeys

    Returns:
        Dict: The JSON data with duplicate explanations removed
    """
    try:
        logger.info("Checking for duplicate explanations in the explanations array")

        # Get the questions and explanations arrays
        questions = json_data.get("questions", [])
        explanations = json_data.get("explanations", [])

        if not explanations:
            logger.info("No explanations found in the explanations array")
            return json_data

        # Group explanations by explanation_number
        explanation_groups = {}
        for explanation in explanations:
            explanation_number = explanation.get("explanation_number")

            # Skip explanations without an explanation_number
            if explanation_number is None:
                continue

            # Convert to string for consistent comparison
            str_explanation_number = str(explanation_number)

            # Add to the appropriate group
            if str_explanation_number not in explanation_groups:
                explanation_groups[str_explanation_number] = []
            explanation_groups[str_explanation_number].append(explanation)

        # Process each group to find duplicates
        duplicates_found = 0
        new_explanations = []

        for explanation_number, group in explanation_groups.items():
            # If there's only one explanation with this number, keep it
            if len(group) == 1:
                new_explanations.append(group[0])
                continue

            # We have duplicates - keep the first one
            duplicates_found += len(group) - 1
            logger.info(f"Found {len(group)} explanations with explanation_number {explanation_number}, keeping the first one")

            # Keep the first explanation
            first_explanation = group[0]
            new_explanations.append(first_explanation)

            # Map the first explanation to any matching question with null explanation
            for question in questions:
                question_number = question.get("question_number")

                # Skip questions without a question_number
                if question_number is None:
                    continue

                # Convert to string for consistent comparison
                str_question_number = str(question_number)

                # If the question number matches the explanation number and the question has no explanation
                if str_question_number == explanation_number and question.get("explanation") is None:
                    question["explanation"] = first_explanation.get("explanation")
                    logger.info(f"Mapped first explanation to question {str_question_number}")

        # Update the explanations array
        json_data["explanations"] = new_explanations
        logger.info(f"Removed {duplicates_found} duplicate explanations from the explanations array")

        return json_data
    except Exception as e:
        logger.error(f"Error removing duplicate explanations: {e}")
        logger.error(traceback.format_exc())
        # Return the original JSON data if there's an error
        return json_data

def map_explanations_from_array(json_data):
    """
    Final check to ensure that if a question has a null explanation but there's a matching explanation
    in the explanations array, that explanation should be added to the question.

    Args:
        json_data: The processed JSON data containing questions, explanations, directions, and answerKeys

    Returns:
        Dict: The JSON data with explanations mapped from the explanations array to questions where needed
    """
    try:
        logger.info("Running final check to map explanations from explanations array to questions")

        # Get the questions and explanations arrays
        questions = json_data.get("questions", [])
        explanations = json_data.get("explanations", [])

        if not explanations:
            logger.info("No explanations found in the explanations array")
            return json_data

        logger.info(f"Found {len(explanations)} explanations in the explanations array")

        # Print the first few explanations for debugging
        for i, explanation in enumerate(explanations[:5]):
            explanation_number = explanation.get("explanation_number")
            explanation_text = explanation.get("explanation", "")[:50] + "..." if len(explanation.get("explanation", "")) > 50 else explanation.get("explanation", "")
            logger.info(f"Explanation {i+1}: number={explanation_number}, text={explanation_text}")

        # Create a dictionary to quickly look up explanations by their explanation_number
        # We'll normalize all keys to strings for consistent comparison
        explanation_dict = {}
        for explanation in explanations:
            explanation_number = explanation.get("explanation_number")
            if explanation_number is not None:
                # Convert to string for consistent comparison
                str_number = str(explanation_number)
                explanation_dict[str_number] = explanation

                # Also try to convert to int and back to string to handle potential formatting differences
                try:
                    if isinstance(explanation_number, str) and explanation_number.isdigit():
                        explanation_dict[str(int(explanation_number))] = explanation
                    elif isinstance(explanation_number, int):
                        explanation_dict[str(explanation_number)] = explanation
                except (ValueError, TypeError):
                    pass

        # Go through all questions and check if they need explanations from the explanations array
        explanations_added = 0
        for question in questions:
            question_number = question.get("question_number")

            # Skip questions without a question_number
            if question_number is None:
                continue

            # Convert to string for consistent comparison
            str_question_number = str(question_number)

            # Check if this question has a null explanation
            if question.get("explanation") is None:
                logger.info(f"Question {str_question_number} has null explanation, looking for a match")

                # Check if we have a matching explanation
                if str_question_number in explanation_dict:
                    explanation = explanation_dict[str_question_number]
                    question["explanation"] = explanation.get("explanation")
                    explanations_added += 1
                    logger.info(f"Added explanation from explanations array to question {str_question_number}")
                else:
                    # Try additional normalization for numeric strings
                    try:
                        if str_question_number.isdigit():
                            normalized_number = str(int(str_question_number))
                            if normalized_number in explanation_dict:
                                explanation = explanation_dict[normalized_number]
                                question["explanation"] = explanation.get("explanation")
                                explanations_added += 1
                                logger.info(f"Added explanation from explanations array to question {str_question_number} using normalized number {normalized_number}")
                                continue
                    except (ValueError, TypeError):
                        pass

                    # If we get here, no match was found
                    logger.info(f"No matching explanation found for question {str_question_number}")
                    logger.info(f"Available explanation numbers: {list(explanation_dict.keys())[:10]}..." if len(explanation_dict) > 10 else list(explanation_dict.keys()))

        logger.info(f"Added explanations to {explanations_added} questions from the explanations array")
        return json_data
    except Exception as e:
        logger.error(f"Error mapping explanations from array: {e}")
        logger.error(traceback.format_exc())
        # Return the original JSON data if there's an error
        return json_data


def process_repeated_questions(json_data):
    """
    Process the merged JSON to handle repeated question objects.
    When a question number appears multiple times, the first occurrence is kept as the main question,
    and subsequent occurrences are treated as explanations for the first question.

    Args:
        json_data: The merged JSON data containing questions, explanations, directions, and answerKeys

    Returns:
        Dict: The processed JSON data with repeated questions handled
    """
    try:
        logger.info("Processing repeated questions as explanations")

        # Get the questions array
        questions = json_data.get("questions", [])
        explanations = json_data.get("explanations", [])

        # Create a dictionary to track questions by their question_number
        question_dict = {}
        processed_questions = []

        # First pass: identify repeated questions and keep track of the first occurrence
        for question in questions:
            question_number = question.get("question_number")

            # Skip questions without a question_number
            if question_number is None:
                processed_questions.append(question)
                continue

            # Convert question_number to string for consistent comparison
            if not isinstance(question_number, str):
                question_number = str(question_number)

            # Skip special cases
            if question_number in ["__prev__", "__next__"]:
                processed_questions.append(question)
                continue

            # Check if we've seen this question number before
            if question_number in question_dict:
                # This is a repeated question - convert it to an explanation for the first occurrence
                first_question = question_dict[question_number]

                # Check if this question has complete information (has options)
                # We need to check both op1-op5 format and options array format
                has_options = False

                # Check for op1-op5 format
                for i in range(1, 6):
                    if question.get(f"op{i}"):
                        has_options = True
                        break

                # Check for options array format
                if question.get("options") and isinstance(question.get("options"), list) and len(question.get("options")) > 0:
                    has_options = True

                # If the first question is complete, treat this one as an explanation
                # regardless of whether this one has options or not
                first_has_options = False
                for i in range(1, 6):
                    if first_question.get(f"op{i}"):
                        first_has_options = True
                        break

                # Also check for options array in first question
                if first_question.get("options") and isinstance(first_question.get("options"), list) and len(first_question.get("options")) > 0:
                    first_has_options = True

                # If the first question has options, always treat the second occurrence as an explanation
                if first_has_options:
                    # First question is complete, so treat the second as an explanation
                    logger.info(f"Found repeated question {question_number}, converting to explanation")

                    # Combine all text fields from the second question
                    explanation_text = question.get("text", "")

                    # Add options text to the explanation if present
                    for i in range(1, 6):
                        option_key = f"op{i}"
                        if question.get(option_key):
                            explanation_text += f"\n{question.get(option_key)}"

                    # Also add options array if present
                    if question.get("options") and isinstance(question.get("options"), list):
                        for option in question.get("options"):
                            if option:
                                explanation_text += f"\n{option}"

                    # Create or update the explanation field in the first question
                    if first_question.get("explanation"):
                        first_question["explanation"] += f"\n{explanation_text}"
                    else:
                        first_question["explanation"] = explanation_text

                    logger.info(f"Added explanation to question {question_number} from repeated question")

                    # Log the updated question for debugging
                    logger.info(f"Updated question {question_number} with explanation from repeated question")
                    logger.info(f"Explanation: {first_question.get('explanation')[:100]}..." if len(first_question.get('explanation', '')) > 100 else first_question.get('explanation', ''))
                else:
                    # If the first question is not complete but this one has options,
                    # update the first question with this one's options
                    if has_options:
                        logger.info(f"Updating incomplete question {question_number} with options from repeated question")

                        # Copy options from this question to the first question
                        for i in range(1, 6):
                            option_key = f"op{i}"
                            if question.get(option_key):
                                first_question[option_key] = question.get(option_key)

                        # Copy options array if present
                        if question.get("options") and isinstance(question.get("options"), list):
                            first_question["options"] = question.get("options")

                        # Also copy correctAnswer if available
                        if question.get("correctAnswer") and not first_question.get("correctAnswer"):
                            first_question["correctAnswer"] = question.get("correctAnswer")
                    else:
                        # If neither question has options, treat the second as an explanation for the first
                        logger.info(f"Found repeated question {question_number} without options, converting to explanation")

                        # Combine all text fields from the second question
                        explanation_text = question.get("text", "")

                        # Create or update the explanation field in the first question
                        if first_question.get("explanation"):
                            first_question["explanation"] += f"\n{explanation_text}"
                        else:
                            first_question["explanation"] = explanation_text

                        logger.info(f"Added explanation to question {question_number} from repeated question without options")
            else:
                # This is the first occurrence of this question number
                question_dict[question_number] = question
                processed_questions.append(question)

        # Log summary of processing
        logger.info(f"Processed {len(questions)} questions, resulting in {len(processed_questions)} unique questions")
        logger.info(f"Identified and processed {len(questions) - len(processed_questions)} repeated questions as explanations")

        # Update the questions array in the JSON data
        json_data["questions"] = processed_questions

        return json_data
    except Exception as e:
        logger.error(f"Error processing repeated questions: {e}")
        logger.error(traceback.format_exc())
        # Return the original JSON data if there's an error
        return json_data


def remove_option_labels(option_text):
    """
    Remove option labels like (1), (a), a), 1), i), (i) from the beginning of option text.

    Args:
        option_text: The option text that may contain labels

    Returns:
        str: The option text with labels removed
    """
    if not option_text or not isinstance(option_text, str):
        return option_text

    import re

    # Pattern to match various option label formats at the beginning of the text
    # Matches: (1), (2), (a), (b), a), b), 1), 2), i), ii), (i), (ii), etc.
    pattern = r'^(\([0-9]+\)|\([a-zA-Z]+\)|[a-zA-Z]+\)|[0-9]+\)|[ivxlcdm]+\)|\([ivxlcdm]+\))\s*'

    # Remove the label and any following whitespace
    cleaned_text = re.sub(pattern, '', option_text.strip(), flags=re.IGNORECASE)

    return cleaned_text.strip()


def wrap_latex_expressions(text):
    """
    Detect and wrap LaTeX expressions with appropriate delimiters for KaTeX rendering.
    Only wraps expressions that are clearly not already wrapped.

    Args:
        text: The text that may contain LaTeX expressions

    Returns:
        str: The text with LaTeX expressions wrapped in \( \) or \[ \] delimiters
    """
    if not text or not isinstance(text, str):
        return text

    import re

    # First, check if the entire text is already wrapped
    if text.startswith('\\(') and text.endswith('\\)'):
        return text
    if text.startswith('\\[') and text.endswith('\\]'):
        return text

    # Check if text already contains properly wrapped LaTeX
    # If it has any wrapped expressions, be more conservative
    has_wrapped_expressions = bool(re.search(r'\\[\(\[].*?\\[\)\]]', text))

    # If text already has wrapped expressions, only wrap clearly unwrapped patterns
    # to avoid over-processing mixed content
    if has_wrapped_expressions:
        logger.debug(f"Text already contains wrapped LaTeX expressions, applying conservative wrapping: {text[:50]}...")

    # Patterns to detect LaTeX expressions (ordered by complexity)
    patterns = [
        # Complex expressions with left/right delimiters first (more comprehensive)
        r'(\\left\s*(?:[()\[\]|]|\\[{}]|[./])\s*.*?\s*\\right\s*(?:[()\[\]|]|\\[{}]|[./]))',  # \left( ... \right), \left\{ ... \right\}, etc.

        # Complex expressions first (to avoid partial matches)
        r'(\d+\^{[^}]+}\s*\\text{[^}]*})',  # e.g., 10^{-20} \text{cm}

        # Fractions - more comprehensive pattern
        r'(\\frac\s*{[^{}]*(?:{[^{}]*}[^{}]*)*}\s*{[^{}]*(?:{[^{}]*}[^{}]*)*})',  # \frac{numerator}{denominator}

        # Square roots and other functions with braces
        r'(\\sqrt\s*{[^{}]*(?:{[^{}]*}[^{}]*)*})',  # \sqrt{content}
        r'(\\[a-zA-Z]+\s*{[^{}]*(?:{[^{}]*}[^{}]*)*})',  # Other commands with braces like \text{...}

        # Mathematical expressions with superscripts/subscripts (with braces)
        r'(\w+\^{[^}]+})',  # e.g., 10^{-20}, x^{2}
        r'(\w+_{[^}]+})',   # e.g., a_{n}

        # Simple superscripts/subscripts without braces
        r'(\w+\^\w+)',      # e.g., x^2, a^n, 10^3
        r'(\w+\^-?\d+)',    # e.g., x^2, x^-1, 10^-3
        r'(\w+_\w+)',       # e.g., x_i, a_n
        r'(\w+_-?\d+)',     # e.g., x_1, a_-1
        r'([A-Za-z]+_\d+[A-Za-z]*)',  # e.g., H_2O, CO_2

        # Mathematical symbols and operators (more comprehensive)
        r'(\\times|\\div|\\pm|\\mp|\\cdot|\\circ|\\ast|\\star)',
        r'(\\leq|\\geq|\\neq|\\approx|\\equiv|\\sim|\\simeq|\\cong)',
        r'(\\in|\\notin|\\subset|\\supset|\\subseteq|\\supseteq)',
        r'(\\cup|\\cap|\\setminus|\\emptyset|\\infty)',
        r'(\\sum|\\prod|\\int|\\oint|\\lim)',

        # Greek letters (comprehensive list)
        r'(\\alpha|\\beta|\\gamma|\\delta|\\epsilon|\\varepsilon|\\zeta|\\eta|\\theta|\\vartheta)',
        r'(\\iota|\\kappa|\\lambda|\\mu|\\nu|\\xi|\\pi|\\varpi|\\rho|\\varrho)',
        r'(\\sigma|\\varsigma|\\tau|\\upsilon|\\phi|\\varphi|\\chi|\\psi|\\omega)',
        r'(\\Gamma|\\Delta|\\Theta|\\Lambda|\\Xi|\\Pi|\\Sigma|\\Upsilon|\\Phi|\\Psi|\\Omega)',

        # Mathematical functions
        r'(\\log|\\ln|\\lg|\\exp|\\sin|\\cos|\\tan|\\cot|\\sec|\\csc)',
        r'(\\arcsin|\\arccos|\\arctan|\\sinh|\\cosh|\\tanh)',
        r'(\\min|\\max|\\sup|\\inf|\\gcd|\\lcm)',

        # Other common LaTeX commands
        r'(\\[a-zA-Z]+)',  # Other backslash commands (catch-all)
    ]

    # Process text to avoid double-wrapping
    result = text

    # Track positions that are already wrapped to avoid double-wrapping
    wrapped_ranges = []

    # Find existing wrapped expressions with more precise pattern
    for match in re.finditer(r'\\[\(\[].*?\\[\)\]]', result):
        wrapped_ranges.append((match.start(), match.end()))

    def is_in_wrapped_range(start, end):
        """Check if a position range overlaps with already wrapped content."""
        for w_start, w_end in wrapped_ranges:
            if not (end <= w_start or start >= w_end):
                return True
        return False

    def is_adjacent_to_wrapped(start, end):
        """Check if expression is immediately adjacent to wrapped content."""
        # Check for delimiters immediately before or after
        before_text = result[max(0, start-3):start]
        after_text = result[end:end+3]

        # Don't wrap if immediately adjacent to math delimiters
        if '\\(' in before_text or '\\)' in after_text:
            return True
        if '\\[' in before_text or '\\]' in after_text:
            return True

        return False

    # Apply patterns in order (most complex first)
    for pattern in patterns:
        matches = list(re.finditer(pattern, result))
        # Process matches in reverse order to maintain positions
        for match in reversed(matches):
            start, end = match.span()
            matched_text = match.group(0)

            # Skip if this range is already wrapped
            if is_in_wrapped_range(start, end):
                continue

            # Skip if adjacent to wrapped content (conservative approach)
            if is_adjacent_to_wrapped(start, end):
                continue

            # If text already has wrapped expressions, be extra conservative
            # Only wrap if the expression is clearly standalone
            if has_wrapped_expressions:
                # Check if this expression is part of a larger mathematical context
                context_before = result[max(0, start-10):start]
                context_after = result[end:end+10]

                # Skip if it appears to be part of already processed math
                if any(delim in context_before + context_after for delim in ['\\(', '\\)', '\\[', '\\]']):
                    continue

            # Wrap with inline math delimiters
            wrapped = f'\\({matched_text}\\)'
            result = result[:start] + wrapped + result[end:]

            # Update wrapped ranges
            wrapped_ranges.append((start, start + len(wrapped)))
            wrapped_ranges.sort()

    return result


def process_latex_in_question(question):
    """
    Process LaTeX expressions in all text fields of a question object.

    Args:
        question: Question object containing text fields that may have LaTeX
    """
    try:
        # Process question text
        if question.get("text"):
            question["text"] = wrap_latex_expressions(question["text"])

        # Process options array if it exists
        if question.get("options") and isinstance(question.get("options"), list):
            question["options"] = [wrap_latex_expressions(option) for option in question["options"]]

        # Process individual option fields (op1, op2, op3, op4, op5)
        for i in range(1, 6):
            option_key = f"op{i}"
            if question.get(option_key):
                question[option_key] = wrap_latex_expressions(question[option_key])

        # Process option1, option2, etc. format if they exist
        for i in range(1, 6):
            option_key = f"option{i}"
            if question.get(option_key):
                question[option_key] = wrap_latex_expressions(question[option_key])

        # Process explanation
        if question.get("explanation"):
            question["explanation"] = wrap_latex_expressions(question["explanation"])

    except Exception as e:
        logger.error(f"Error processing LaTeX for question {question.get('question_number', 'unknown')}: {e}")


def process_latex_in_explanation(explanation):
    """
    Process LaTeX expressions in explanation text fields.

    Args:
        explanation: Explanation object containing text fields that may have LaTeX
    """
    try:
        # Process explanation text
        if explanation.get("explanation"):
            explanation["explanation"] = wrap_latex_expressions(explanation["explanation"])

        # Process text field if it exists (for explanations in question format)
        if explanation.get("text"):
            explanation["text"] = wrap_latex_expressions(explanation["text"])

    except Exception as e:
        logger.error(f"Error processing LaTeX for explanation {explanation.get('explanation_number', 'unknown')}: {e}")


def process_latex_in_direction(direction):
    """
    Process LaTeX expressions in direction text fields.

    Args:
        direction: Direction object containing text fields that may have LaTeX
    """
    try:
        # Process direction text
        if direction.get("text"):
            direction["text"] = wrap_latex_expressions(direction["text"])

    except Exception as e:
        logger.error(f"Error processing LaTeX for direction {direction.get('direction_id', 'unknown')}: {e}")


def clean_question_options(question):
    """
    Clean option labels from all option fields in a question object.

    Args:
        question: Question object containing option fields
    """
    try:
        # Clean options array if it exists
        if question.get("options") and isinstance(question.get("options"), list):
            question["options"] = [remove_option_labels(option) for option in question["options"]]

        # Clean individual option fields (op1, op2, op3, op4, op5)
        for i in range(1, 6):
            option_key = f"op{i}"
            if question.get(option_key):
                question[option_key] = remove_option_labels(question[option_key])

        # Also clean option1, option2, etc. format if they exist
        for i in range(1, 6):
            option_key = f"option{i}"
            if question.get(option_key):
                question[option_key] = remove_option_labels(question[option_key])

    except Exception as e:
        logger.error(f"Error cleaning options for question {question.get('question_number', 'unknown')}: {e}")


def merge_json_files(output_dir: str, resource_id: str) -> Dict:
    """
    Merge all JSON files in the output directory into a single consolidated JSON file.

    Args:
        output_dir: Path to the directory containing JSON files
        resource_id: Resource ID

    Returns:
        Dict: Status of the merging process
    """
    try:
        logger.info(f"Starting to merge JSON files in {output_dir}")

        # Get a list of all JSON files in the output directory
        json_files = [f for f in os.listdir(output_dir) if f.endswith(".json")]

        if not json_files:
            logger.warning(f"No JSON files found in {output_dir}")
            return {
                "status": "error",
                "message": f"No JSON files found in {output_dir}"
            }

        logger.info(f"Found {len(json_files)} JSON files to merge")

        # Initialize consolidated arrays
        consolidated_questions = []
        consolidated_explanations = []
        consolidated_directions = []
        consolidated_answer_keys = []

        # Import re module at the beginning of the function to ensure it's available in all inner functions
        import re

        # Extract page and column numbers for sorting
        def extract_page_col(filename):
            page_match = re.search(r'page_([0-9]+)', filename)
            col_match = re.search(r'col_([0-9]+)', filename)

            page_num = int(page_match.group(1)) if page_match else 0
            col_num = int(col_match.group(1)) if col_match else 0

            return (page_num, col_num)

        # Sort files by page number and column number to ensure sequential processing
        sorted_files = sorted(json_files, key=extract_page_col)
        logger.info(f"Sorted files for sequential processing: {sorted_files}")

        # Flag to identify when we've encountered the first explanation
        found_first_explanation = False

        # Create a dictionary to track questions by their question_number
        question_dict = {}

        # Process each JSON file in sequential order
        for json_file in sorted_files:
            file_path = os.path.join(output_dir, json_file)
            logger.info(f"Processing file: {json_file}")

            try:
                data = safe_read_json(file_path)
                if not data:
                    logger.error(f"Failed to read JSON file {file_path}, skipping")
                    continue

                # Check if this file represents a skipped extraction
                if data.get("content") and isinstance(data["content"], str) and "SKIPPED_DUE_TO_504" in data["content"]:
                    logger.warning(f"Found skipped extraction in {file_path}")
                    # Extract page/column info if available
                    context_info = ""
                    if "page" in file_path and "col" in file_path:
                        # re module is already imported at the beginning of the function
                        page_match = re.search(r'page_([0-9]+)', os.path.basename(file_path))
                        col_match = re.search(r'col_([0-9]+)', os.path.basename(file_path))
                        if page_match and col_match:
                            context_info = f"page {page_match.group(1)} column {col_match.group(1)}"

                    # Add a note about skipped content but continue processing
                    logger.warning(f"Skipping content from {context_info if context_info else os.path.basename(file_path)} due to 504 error")
                    continue

                # Extract content from the JSON file
                content = data.get("content", {})

                # Extract questions, explanations, directions, and answer keys
                questions = content.get("questions", [])
                explanations = content.get("explanations", [])
                directions = content.get("directions", [])
                answer_keys = content.get("answerKeys", [])

                logger.info(f"Extracted {len(questions)} questions, {len(explanations)} explanations, {len(directions)} directions, and {len(answer_keys)} answer keys from {json_file}")

                # Clean option labels and process LaTeX from all questions immediately after extraction
                questions_processed = 0
                for question in questions:
                    clean_question_options(question)
                    process_latex_in_question(question)
                    questions_processed += 1
                if questions_processed > 0:
                    logger.info(f"Cleaned option labels and processed LaTeX for {questions_processed} questions in {json_file}")

                # Process LaTeX in explanations
                explanations_processed = 0
                for explanation in explanations:
                    process_latex_in_explanation(explanation)
                    explanations_processed += 1
                if explanations_processed > 0:
                    logger.info(f"Processed LaTeX for {explanations_processed} explanations in {json_file}")

                # Process LaTeX in directions
                directions_processed = 0
                for direction in directions:
                    process_latex_in_direction(direction)
                    directions_processed += 1
                if directions_processed > 0:
                    logger.info(f"Processed LaTeX for {directions_processed} directions in {json_file}")

                # Check if we've found explanations
                if explanations and not found_first_explanation:
                    found_first_explanation = True
                    logger.info("Found first explanation. Will treat subsequent questions without options as explanations.")

                # Process questions and handle repeated question numbers
                processed_questions = []
                for q in questions:
                    question_number = q.get("question_number")

                    # Skip questions without a question_number
                    if question_number is None:
                        processed_questions.append(q)
                        continue

                    # Convert question_number to string for consistent comparison
                    if not isinstance(question_number, str):
                        question_number = str(question_number)

                    # Skip special cases
                    if question_number in ["__prev__", "__next__"]:
                        processed_questions.append(q)
                        continue

                    # Check if this question has options
                    has_options = False

                    # Check for options array
                    if q.get("options") and isinstance(q.get("options"), list) and len(q.get("options", [])) > 0:
                        has_options = True

                    # Check for individual option fields (op1, op2, etc.)
                    for i in range(1, 6):
                        if q.get(f"op{i}"):
                            has_options = True
                            break

                    # Check if we've seen this question number before
                    if question_number in question_dict:
                        # This is a repeated question - convert it to an explanation for the first occurrence
                        first_question = question_dict[question_number]

                        # Check if the first question has options
                        first_has_options = False

                        # Check for options array in first question
                        if first_question.get("options") and isinstance(first_question.get("options"), list) and len(first_question.get("options", [])) > 0:
                            first_has_options = True

                        # Check for individual option fields in first question
                        for i in range(1, 6):
                            if first_question.get(f"op{i}"):
                                first_has_options = True
                                break

                        # If the first question has options, always treat the second occurrence as an explanation
                        if first_has_options:
                            # First question is complete, so treat the second as an explanation
                            logger.info(f"Found repeated question {question_number} in file {json_file}, converting to explanation")

                            # Combine all text fields from the second question
                            explanation_text = q.get("text", "")
                            passage = q.get("passage")

                            if passage:
                                if explanation_text:
                                    explanation_text = explanation_text + "\n" + passage
                                else:
                                    explanation_text = passage

                            # Add options text to the explanation if present
                            if q.get("options") and isinstance(q.get("options"), list):
                                for option in q.get("options"):
                                    if option:
                                        explanation_text += f"\n{option}"

                            # Add individual option fields to the explanation if present
                            for i in range(1, 6):
                                option_key = f"op{i}"
                                if q.get(option_key):
                                    explanation_text += f"\n{q.get(option_key)}"

                            # Create an explanation object
                            explanation_obj = {
                                "explanation_number": question_number,
                                "explanation": explanation_text,
                                "page": q.get("page"),
                                "partial": q.get("partial", "none")
                            }

                            # Add explanation_images if question_images exist
                            if q.get("question_images"):
                                explanation_obj["explanation_images"] = q.get("question_images")

                            # Add to consolidated explanations
                            consolidated_explanations.append(explanation_obj)
                            logger.info(f"Added explanation for question {question_number} from repeated question")
                        else:
                            # If the first question is not complete but this one has options,
                            # update the first question with this one's options
                            if has_options:
                                logger.info(f"Updating incomplete question {question_number} with options from repeated question in file {json_file}")

                                # Copy options from this question to the first question
                                if q.get("options") and isinstance(q.get("options"), list):
                                    cleaned_options = [remove_option_labels(option) for option in q.get("options")]
                                    first_question["options"] = [wrap_latex_expressions(option) for option in cleaned_options]

                                # Copy individual option fields
                                for i in range(1, 6):
                                    option_key = f"op{i}"
                                    if q.get(option_key):
                                        cleaned_option = remove_option_labels(q.get(option_key))
                                        first_question[option_key] = wrap_latex_expressions(cleaned_option)

                                # Also copy correctAnswer if available
                                if q.get("correctAnswer") and not first_question.get("correctAnswer"):
                                    first_question["correctAnswer"] = q.get("correctAnswer")
                            else:
                                # If neither question has options, treat the second as an explanation for the first
                                logger.info(f"Found repeated question {question_number} without options in file {json_file}, converting to explanation")

                                # Combine all text fields from the second question
                                explanation_text = q.get("text", "")
                                passage = q.get("passage")

                                if passage:
                                    if explanation_text:
                                        explanation_text = explanation_text + "\n" + passage
                                    else:
                                        explanation_text = passage

                                # Create an explanation object
                                explanation_obj = {
                                    "explanation_number": question_number,
                                    "explanation": explanation_text,
                                    "page": q.get("page"),
                                    "partial": q.get("partial", "none")
                                }

                                # Add explanation_images if question_images exist
                                if q.get("question_images"):
                                    explanation_obj["explanation_images"] = q.get("question_images")

                                # Add to consolidated explanations
                                consolidated_explanations.append(explanation_obj)
                                logger.info(f"Added explanation for question {question_number} from repeated question without options")
                    else:
                        # This is the first occurrence of this question number
                        question_dict[question_number] = q
                        processed_questions.append(q)

                # Add to consolidated arrays based on whether we've found explanations
                if found_first_explanation:
                    # Separate questions into actual questions (with options) and explanations (without options)
                    questions_with_options = []
                    questions_without_options = []

                    for q in processed_questions:
                        # Skip questions that have already been processed for repeated question numbers
                        if q.get("question_number") and str(q.get("question_number")) in question_dict and question_dict[str(q.get("question_number"))] != q:
                            continue

                        # Check if this item has options
                        has_options = False

                        # Check for options array
                        if q.get("options") and len(q.get("options", [])) > 0:
                            has_options = True

                        # Check for individual option fields (option1, option2, etc.)
                        for i in range(1, 6):  # Check option1 through option5
                            if q.get(f"option{i}") or q.get(f"op{i}"):
                                has_options = True
                                break

                        # Special case: if question_number is __prev__ and it has options,
                        # it should always go to questions array for proper merging
                        if q.get("question_number") == "__prev__" and has_options:
                            questions_with_options.append(q)
                        elif has_options:
                            questions_with_options.append(q)
                        else:
                            # Convert question object to explanation object structure
                            # Combine text and passage if both exist
                            explanation_text = q.get("text", "")
                            passage = q.get("passage")

                            if passage:
                                if explanation_text:
                                    explanation_text = explanation_text + "\n" + passage
                                else:
                                    explanation_text = passage

                            explanation_obj = {
                                "explanation_number": q.get("question_number"),
                                "explanation": explanation_text,
                                "page": q.get("page"),
                                "partial": q.get("partial", "none")
                            }

                            # Add explanation_images if question_images exist
                            if q.get("question_images"):
                                explanation_obj["explanation_images"] = q.get("question_images")
                            questions_without_options.append(explanation_obj)

                    logger.info(f"Splitting questions array: {len(questions_with_options)} with options (added to questions), "
                              f"{len(questions_without_options)} without options (converted to explanation format)")

                    consolidated_questions.extend(questions_with_options)
                    consolidated_explanations.extend(questions_without_options)
                else:
                    # Add all processed questions to consolidated questions
                    for q in processed_questions:
                        # Skip questions that have already been processed for repeated question numbers
                        if q.get("question_number") and str(q.get("question_number")) in question_dict and question_dict[str(q.get("question_number"))] != q:
                            continue
                        consolidated_questions.append(q)

                # Process and add explanations to the explanations array
                for explanation in explanations:
                    # Ensure explanation has the correct structure
                    if "text" in explanation and "explanation" not in explanation:
                        # Convert from question format to explanation format
                        # Combine text and passage if both exist
                        explanation_text = explanation.get("text", "")
                        passage = explanation.get("passage")

                        if passage:
                            if explanation_text:
                                explanation_text = explanation_text + "\n" + passage
                            else:
                                explanation_text = passage

                        explanation_obj = {
                            "explanation_number": explanation.get("question_number"),
                            "explanation": explanation_text,
                            "page": explanation.get("page"),
                            "partial": explanation.get("partial", "none")
                        }

                        # Add explanation_images if question_images exist
                        if explanation.get("question_images"):
                            explanation_obj["explanation_images"] = explanation.get("question_images")
                        consolidated_explanations.append(explanation_obj)
                    else:
                        consolidated_explanations.append(explanation)
                consolidated_directions.extend(directions)
                consolidated_answer_keys.extend(answer_keys)

            except Exception as e:
                logger.error(f"Error processing file {json_file}: {e}")
                logger.error(traceback.format_exc())
                continue

        # Sort questions by question_number to ensure sequential order
        def get_question_number(q):
            try:
                num = q.get("question_number")
                if num == "__prev__" or num is None:
                    return float('inf')  # Place at the end
                return int(num) if isinstance(num, (int, str)) else float('inf')
            except (ValueError, TypeError):
                return float('inf')  # Place at the end

        # Sort questions by question_number
        sorted_questions = sorted([q for q in consolidated_questions if q.get("question_number") != "__prev__"],
                                 key=get_question_number)

        # Get questions with __prev__ to process separately
        prev_questions = [q for q in consolidated_questions if q.get("question_number") == "__prev__"]
        logger.info(f"Found {len(prev_questions)} questions with question_number='__prev__'")

        # Log details of each __prev__ question for debugging
        for i, q in enumerate(prev_questions):
            has_options_array = bool(q.get("options"))
            has_individual_options = any(q.get(f"op{j}") for j in range(1, 6))
            logger.info(f"__prev__ question {i+1}: partial={q.get('partial')}, has_options_array={has_options_array}, has_individual_options={has_individual_options}")
            logger.info(f"__prev__ question {i+1} full data: {json.dumps(q, indent=2)}")

        # Process questions with "__prev__" question_number
        processed_questions = []
        current_question_index = 0

        # First add all sorted questions
        for question in sorted_questions:
            processed_questions.append(question)

        # Then process __prev__ questions
        for question in prev_questions:
            if processed_questions:
                # Find the appropriate question to merge with
                # By default, we merge with the last question
                prev_question = processed_questions[-1]

                # Check if this __prev__ question has options that match a specific question
                # This is a heuristic to try to match __prev__ questions with the correct question
                # based on the options content
                if question.get("options"):
                    # Look for patterns in the options that might indicate which question this belongs to
                    options_text = "\n".join(question.get("options", []))

                    # Pattern 1: options containing triplets of numbers like (42, 18, 1008)
                    # This likely belongs to question 4 based on the example
                    triplet_pattern = re.search(r'\([0-9]+, [0-9]+, [0-9]+\)', options_text)
                    if triplet_pattern:
                        # Try to find question 4 with type Short
                        for q in processed_questions:
                            if q.get("question_number") == "4" and q.get("type") == "Short":
                                prev_question = q
                                logger.info(f"Found matching question 4 (type: Short) for __prev__ with triplet pattern")
                                break

                        # If we couldn't find question 4 with type Short, try any question 4
                        if prev_question == processed_questions[-1]:  # If we're still using the default
                            for q in processed_questions:
                                if q.get("question_number") == "4":
                                    prev_question = q
                                    logger.info(f"Found matching question 4 (type: {q.get('type')}) for __prev__ with triplet pattern")
                                    break

                    # If we couldn't find a specific match, try to find the question with missing options
                    # This is a more general approach that should work in most cases
                    if prev_question == processed_questions[-1]:  # If we're still using the default (last question)
                        for q in processed_questions:
                            # If a question has null options or empty options array, it's a good candidate
                            if q.get("options") is None or (isinstance(q.get("options"), list) and len(q.get("options", [])) == 0):
                                # Also check that it doesn't have individual option fields
                                has_individual_options = any(q.get(f"op{j}") for j in range(1, 6))
                                if not has_individual_options:
                                    prev_question = q
                                    logger.info(f"Found question {q.get('question_number')} with missing options for __prev__")
                                    break

                logger.info(f"Merging question with '__prev__' to question {prev_question.get('question_number')}")

                # Merge text if needed
                # First, combine text and passage from the __prev__ question if both exist
                prev_text = question.get("text", "")
                prev_passage = question.get("passage")

                if prev_passage and prev_passage not in [None, ""]:
                    if prev_text:
                        prev_text = prev_text + "\n" + prev_passage
                    else:
                        prev_text = prev_passage

                if prev_text:  # If there's any text to merge
                    # If the previous question has text, always append the __prev__ text to it
                    if prev_question.get("text"):
                        # Only append if the text is not already there (avoid duplication)
                        if prev_text not in prev_question["text"]:
                            prev_question["text"] = prev_question["text"] + "\n" + prev_text
                            logger.info(f"Appended text from __prev__ question to question {prev_question.get('question_number')}")
                    else:
                        # If the previous question doesn't have text, use the text from __prev__
                        prev_question["text"] = prev_text
                        logger.info(f"Set text from __prev__ question to question {prev_question.get('question_number')}")

                # Merge options - for __prev__, we always use the options from the __prev__ question
                if question.get("options"):
                    # If the previous question already has options, log a warning
                    if prev_question.get("options"):
                        logger.warning(f"Question {prev_question.get('question_number')} already has options, overwriting with __prev__ options")
                        logger.warning(f"Original options: {json.dumps(prev_question.get('options'))}")
                        logger.warning(f"New options: {json.dumps(question.get('options'))}")

                    cleaned_options = [remove_option_labels(option) for option in question["options"]]
                    prev_question["options"] = [wrap_latex_expressions(option) for option in cleaned_options]
                    logger.info(f"Set options for question {prev_question.get('question_number')} from __prev__ question")

                # Also merge individual option fields (op1, op2, etc.) if present
                has_individual_options = False
                for i in range(1, 6):  # Check option1 through option5
                    option_key = f"op{i}"
                    if question.get(option_key):
                        # If the previous question already has this option, log a warning
                        if prev_question.get(option_key):
                            logger.warning(f"Question {prev_question.get('question_number')} already has {option_key}, overwriting with __prev__ {option_key}")
                            logger.warning(f"Original {option_key}: {prev_question.get(option_key)}")
                            logger.warning(f"New {option_key}: {question.get(option_key)}")

                        cleaned_option = remove_option_labels(question[option_key])
                        prev_question[option_key] = wrap_latex_expressions(cleaned_option)
                        has_individual_options = True
                        logger.info(f"Set {option_key} for question {prev_question.get('question_number')} from __prev__ question")

                # Update partial field if needed
                if question.get("partial") == "end":
                    prev_question["partial"] = "none"
                    logger.info(f"Updated partial field to 'none' for question {prev_question.get('question_number')}")

                # Log the result of the merge for debugging
                has_options_array_after = bool(prev_question.get("options"))
                has_individual_options_after = any(prev_question.get(f"op{j}") for j in range(1, 6))
                text_length = len(prev_question.get("text", ""))

                logger.info(f"After merging __prev__ question: question_number={prev_question.get('question_number')}, "
                           f"partial={prev_question.get('partial')}, has_options_array={has_options_array_after}, "
                           f"has_individual_options={has_individual_options_after}, text_length={text_length}")

                # Log the first 100 characters of the text for verification
                if text_length > 0:
                    text_preview = prev_question.get("text", "")[:100] + "..." if text_length > 100 else prev_question.get("text", "")
                    logger.info(f"Text preview: {text_preview}")
            else:
                logger.warning("Found __prev__ question but no previous question to merge with")

        # Sort explanations by explanation_number or question_number
        def get_explanation_number(e):
            try:
                # First check for explanation_number, then fall back to question_number
                num = None
                if "explanation_number" in e:
                    num = e.get("explanation_number")
                elif "question_number" in e:
                    num = e.get("question_number")

                if num == "__prev__" or num is None:
                    return float('inf')  # Place at the end

                # Try to convert to int for proper sorting
                return int(num) if isinstance(num, (int, str)) else float('inf')
            except (ValueError, TypeError):
                return float('inf')  # Place at the end

        # Sort explanations by explanation_number or question_number
        sorted_explanations = sorted([e for e in consolidated_explanations if
                                     (e.get("explanation_number") != "__prev__" and e.get("question_number") != "__prev__")],
                                    key=get_explanation_number)

        # Get explanations with __prev__ to process separately
        prev_explanations = []
        for e in consolidated_explanations:
            if e.get("explanation_number") == "__prev__" or e.get("question_number") == "__prev__":
                # Ensure explanation has the correct structure
                if "question_number" in e and "explanation_number" not in e:
                    # Convert from question format to explanation format if needed
                    # Combine text and passage if both exist
                    explanation_text = e.get("text", "")
                    passage = e.get("passage")

                    if passage:
                        if explanation_text:
                            explanation_text = explanation_text + "\n" + passage
                        else:
                            explanation_text = passage

                    explanation_obj = {
                        "explanation_number": "__prev__",
                        "explanation": explanation_text,
                        "page": e.get("page"),
                        "partial": e.get("partial", "none")
                    }

                    # Add explanation_images if question_images exist
                    if e.get("question_images"):
                        explanation_obj["explanation_images"] = e.get("question_images")
                    prev_explanations.append(explanation_obj)
                else:
                    prev_explanations.append(e)

        # Process explanations with "__prev__" explanation_number
        processed_explanations = []

        # First add all sorted explanations
        for explanation in sorted_explanations:
            processed_explanations.append(explanation)

        # Then process __prev__ explanations
        for explanation in prev_explanations:
            if processed_explanations:
                # Find the appropriate explanation to merge with
                # For __prev__, we need to find the correct explanation to merge with
                # based on the context and position in the file

                # Default to the last explanation
                prev_explanation = processed_explanations[-1]

                # Check if this __prev__ explanation has options - if so, it might be a question
                # that was incorrectly classified as an explanation
                has_options = False
                if explanation.get("options") and len(explanation.get("options", [])) > 0:
                    has_options = True
                for i in range(1, 6):  # Check option1 through option5
                    if explanation.get(f"option{i}"):
                        has_options = True
                        break

                if has_options:
                    logger.warning(f"Found __prev__ explanation with options. This might be a question that was incorrectly classified.")
                    # We'll still process it as an explanation as requested

                logger.info(f"Merging explanation with '__prev__' to explanation {prev_explanation.get('explanation_number') or prev_explanation.get('question_number')}")

                # Ensure prev_explanation has the correct structure
                if "text" in prev_explanation and "explanation" not in prev_explanation:
                    # Convert from question format to explanation format
                    prev_explanation["explanation"] = prev_explanation.pop("text", "")
                    if "question_number" in prev_explanation and "explanation_number" not in prev_explanation:
                        prev_explanation["explanation_number"] = prev_explanation.pop("question_number")

                # Merge text - prioritize the explanation field
                if explanation.get("explanation"):
                    if prev_explanation.get("explanation"):
                        prev_explanation["explanation"] = prev_explanation["explanation"] + "\n" + explanation["explanation"]
                    else:
                        prev_explanation["explanation"] = explanation["explanation"]
                elif explanation.get("text"):
                    # If the __prev__ item has text instead of explanation, convert it
                    if prev_explanation.get("explanation"):
                        prev_explanation["explanation"] = prev_explanation["explanation"] + "\n" + explanation["text"]
                    else:
                        prev_explanation["explanation"] = explanation["text"]

                # If this __prev__ explanation has options, merge them too
                if has_options:
                    if explanation.get("options"):
                        prev_explanation["options"] = explanation["options"]

                    # Also merge individual option fields if present
                    for i in range(1, 6):  # option1 through option5
                        option_key = f"option{i}"
                        if explanation.get(option_key):
                            prev_explanation[option_key] = explanation[option_key]

                # Merge question_images to explanation_images if present
                if explanation.get("question_images"):
                    if "explanation_images" in prev_explanation:
                        # Append to existing explanation_images
                        prev_explanation["explanation_images"].extend(explanation["question_images"])
                    else:
                        # Create new explanation_images field
                        prev_explanation["explanation_images"] = explanation["question_images"]

                # Also handle direct explanation_images if present
                if explanation.get("explanation_images"):
                    if "explanation_images" in prev_explanation:
                        # Append to existing explanation_images
                        prev_explanation["explanation_images"].extend(explanation["explanation_images"])
                    else:
                        # Create new explanation_images field
                        prev_explanation["explanation_images"] = explanation["explanation_images"]
            else:
                logger.warning("Found __prev__ explanation but no previous explanation to merge with")

        # Process directions with "__prev__" direction_id
        processed_directions = []
        for direction in consolidated_directions:
            direction_id = direction.get("direction_id") or direction.get("id")

            if direction_id == "__prev__" and processed_directions:
                # Find the previous direction to merge with
                prev_direction = processed_directions[-1]

                logger.info(f"Merging direction with '__prev__' to previous direction {prev_direction.get('direction_id') or prev_direction.get('id')}")

                # Merge text if both have text
                if direction.get("text") and prev_direction.get("text"):
                    prev_direction["text"] = prev_direction["text"] + "\n" + direction["text"]
                elif direction.get("text"):
                    prev_direction["text"] = direction["text"]
            else:
                # Add the direction to the processed list
                processed_directions.append(direction)

        # Final cleanup to ensure all explanations have the correct structure
        final_explanations = []
        for explanation in processed_explanations:
            # Check if this explanation has the correct structure
            if "explanation" not in explanation and "text" in explanation:
                # Convert from question format to explanation format
                # Combine text and passage if both exist
                explanation_text = explanation.get("text", "")
                passage = explanation.get("passage")

                if passage:
                    if explanation_text:
                        explanation_text = explanation_text + "\n" + passage
                    else:
                        explanation_text = passage

                explanation_obj = {
                    "explanation_number": explanation.get("question_number") or explanation.get("explanation_number"),
                    "explanation": explanation_text,
                    "page": explanation.get("page"),
                    "partial": explanation.get("partial", "none")
                }

                # Add explanation_images if question_images exist
                if explanation.get("question_images"):
                    explanation_obj["explanation_images"] = explanation.get("question_images")
                final_explanations.append(explanation_obj)
            else:
                final_explanations.append(explanation)

        # Ensure direction IDs are sequential and unique
        logger.info("Ensuring direction IDs are sequential and unique")

        # Simply regenerate all direction IDs in sequential order
        for i, direction in enumerate(processed_directions):
            # Generate a sequential ID starting from 1
            new_id = f"D{i + 1}"

            # Log if we're changing an existing ID
            if direction.get("id") and direction.get("id") != new_id:
                logger.info(f"Changing direction ID from {direction.get('id')} to {new_id}")
            elif not direction.get("id"):
                logger.info(f"Assigning new direction ID: {new_id}")

            # Set the new sequential ID
            direction["id"] = new_id

        # First, initialize all questions with directionsId = null
        logger.info("Initializing all questions with directionsId = null")
        for question in processed_questions:
            question["directionsId"] = None

        # Map directions to questions based on the appliesTo field
        logger.info("Mapping directions to questions based on appliesTo field")
        for direction in processed_directions:
            direction_id = direction.get("id")
            applies_to = direction.get("appliesTo")

            # Skip if direction_id is missing
            if not direction_id:
                logger.warning(f"Skipping direction with missing id: {direction}")
                continue

            # Handle missing or special appliesTo field
            if not applies_to:
                logger.warning(f"Direction {direction_id} has no appliesTo field, skipping")
                continue

            # Handle special cases like "next" or other non-numeric values
            if applies_to.lower() in ["next", "all", "none"]:
                logger.warning(f"Direction {direction_id} has special appliesTo value '{applies_to}', skipping")
                continue

            # Check if appliesTo can be parsed as a number range, list, or single number
            is_valid_format = False
            if "-" in applies_to:
                # Check if it's a valid range format (e.g., "1-6")
                try:
                    parts = applies_to.split("-")
                    if len(parts) == 2 and parts[0].strip().isdigit() and parts[1].strip().isdigit():
                        is_valid_format = True
                except:
                    pass
            elif "," in applies_to:
                # Check if it's a valid list format (e.g., "1,2,3")
                try:
                    parts = applies_to.split(",")
                    if all(part.strip().isdigit() for part in parts):
                        is_valid_format = True
                except:
                    pass
            else:
                # Check if it's a valid single number
                if applies_to.strip().isdigit():
                    is_valid_format = True

            if not is_valid_format:
                logger.warning(f"Direction {direction_id} has invalid appliesTo format '{applies_to}', skipping")
                continue

            # Now process the direction
            logger.info(f"Processing direction {direction_id} with appliesTo: {applies_to}")

            # Handle different formats of appliesTo field
            if "-" in applies_to:  # Range format like "1-6"
                try:
                    start, end = applies_to.split("-")
                    start_num = int(start.strip())
                    end_num = int(end.strip())

                    # Find questions in this range and add directionsId
                    for question in processed_questions:
                        question_num = question.get("question_number")
                        if question_num and isinstance(question_num, (int, str)):
                            try:
                                q_num = int(question_num)
                                if start_num <= q_num <= end_num:
                                    question["directionsId"] = direction_id
                                    logger.info(f"Added directionsId {direction_id} to question {question_num}")
                            except (ValueError, TypeError):
                                logger.warning(f"Could not convert question_number {question_num} to int")
                except Exception as e:
                    logger.warning(f"Error processing appliesTo range {applies_to}: {e}")

            elif "," in applies_to:  # List format like "1,2,3"
                try:
                    question_nums = [int(num.strip()) for num in applies_to.split(",")]

                    # Find questions in this list and add directionsId
                    for question in processed_questions:
                        question_num = question.get("question_number")
                        if question_num and isinstance(question_num, (int, str)):
                            try:
                                q_num = int(question_num)
                                if q_num in question_nums:
                                    question["directionsId"] = direction_id
                                    logger.info(f"Added directionsId {direction_id} to question {question_num}")
                            except (ValueError, TypeError):
                                logger.warning(f"Could not convert question_number {question_num} to int")
                except Exception as e:
                    logger.warning(f"Error processing appliesTo list {applies_to}: {e}")

            else:  # Single number format like "17"
                try:
                    applies_to_num = int(applies_to.strip())

                    # Find the question with this number and add directionsId
                    for question in processed_questions:
                        question_num = question.get("question_number")
                        if question_num and isinstance(question_num, (int, str)):
                            try:
                                q_num = int(question_num)
                                if q_num == applies_to_num:
                                    question["directionsId"] = direction_id
                                    logger.info(f"Added directionsId {direction_id} to question {question_num}")
                            except (ValueError, TypeError):
                                logger.warning(f"Could not convert question_number {question_num} to int")
                except Exception as e:
                    logger.warning(f"Error processing appliesTo single number {applies_to}: {e}")


        # Remove invalid directions
        logger.info("Removing invalid directions based on question count")
        valid_directions = []
        questions_count = len(processed_questions)

        for direction in processed_directions:
            direction_id = direction.get("id")
            applies_to = direction.get("appliesTo")

            # Skip if appliesTo is missing
            if not applies_to:
                logger.warning(f"Direction {direction_id} has no appliesTo field, skipping")
                continue

            # Skip special cases like "next" which are invalid
            if applies_to.lower() in ["next", "all", "none"]:
                logger.warning(f"Direction {direction_id} has invalid special appliesTo value '{applies_to}', removing")
                continue

            # Check if appliesTo is valid based on question count
            is_valid = False

            if "-" in applies_to:  # Range format like "1-6"
                try:
                    start, end = applies_to.split("-")
                    start_num = int(start.strip())
                    end_num = int(end.strip())

                    # Check if the range is within the valid question count
                    if start_num <= questions_count and start_num > 0:
                        is_valid = True
                    else:
                        logger.warning(f"Direction {direction_id} has invalid appliesTo range '{applies_to}', start number {start_num} is outside valid question count {questions_count}, removing")
                except Exception as e:
                    logger.warning(f"Direction {direction_id} has invalid appliesTo format '{applies_to}', removing: {e}")
            elif "," in applies_to:  # List format like "1,2,3"
                try:
                    question_nums = [int(num.strip()) for num in applies_to.split(",")]

                    # Check if any of the question numbers are within the valid range
                    valid_nums = [num for num in question_nums if 0 < num <= questions_count]
                    if valid_nums:
                        is_valid = True
                    else:
                        logger.warning(f"Direction {direction_id} has invalid appliesTo list '{applies_to}', all numbers are outside valid question count {questions_count}, removing")
                except Exception as e:
                    logger.warning(f"Direction {direction_id} has invalid appliesTo format '{applies_to}', removing: {e}")
            else:  # Single number format like "17"
                try:
                    applies_to_num = int(applies_to.strip())

                    # Check if the number is within the valid question count
                    if 0 < applies_to_num <= questions_count:
                        is_valid = True
                    else:
                        logger.warning(f"Direction {direction_id} has invalid appliesTo number '{applies_to}', outside valid question count {questions_count}, removing")
                except Exception as e:
                    logger.warning(f"Direction {direction_id} has invalid appliesTo format '{applies_to}', removing: {e}")

            if is_valid:
                valid_directions.append(direction)
                logger.info(f"Kept valid direction {direction_id} with appliesTo '{applies_to}'")

        # Log summary of direction filtering
        removed_count = len(processed_directions) - len(valid_directions)
        logger.info(f"Removed {removed_count} invalid directions, kept {len(valid_directions)} valid directions")

        # Update processed_directions with the filtered list
        processed_directions = valid_directions

        # Create the final JSON structure
        final_json = {
            "questions": processed_questions,
            "explanations": final_explanations,
            "directions": processed_directions,
            "answerKeys": consolidated_answer_keys
        }

        # Process repeated questions as explanations (as a final step to catch any remaining repeated questions)
        logger.info("Running final pass to catch any remaining repeated questions")
        processed_json = process_repeated_questions(final_json)

        # Final check list: Map explanations from explanations array to questions with null explanations
        processed_json = map_explanations_from_array(processed_json)

        # Check for duplicate explanations in the explanations array and remove them
        processed_json = remove_duplicate_explanations(processed_json)

        # Save the final JSON to the output directory
        final_json_path = os.path.join(output_dir, f"{resource_id}.json")

        if not safe_write_json(final_json_path, processed_json):
            return {"status": "error", "message": f"Failed to write final JSON file {final_json_path}"}

        logger.info(f"Saved merged JSON to {final_json_path}")

        return {
            "status": "success",
            "message": f"Merged {len(json_files)} JSON files into {final_json_path}",
            "file_path": final_json_path,
            "questions_count": len(processed_json['questions']),
            "explanations_count": len(processed_json['explanations']),
            "directions_count": len(processed_json['directions']),
            "answer_keys_count": len(processed_json['answerKeys'])
        }

    except Exception as e:
        logger.error(f"Error merging JSON files: {e}")
        logger.error(traceback.format_exc())
        return {"status": "error", "message": str(e)}
